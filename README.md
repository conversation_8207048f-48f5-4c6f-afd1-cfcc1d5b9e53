# 目录管理器

一个功能完整的目录管理器应用程序，使用 Python 和 PyQt6 构建，专为 Linux 环境（特别是 WSL2）设计。

## 特性

### 核心功能
- **双面板界面**: 左侧树视图显示目录结构，右侧列表视图显示选中目录的内容
- **内存树数据结构**: 高效的目录层次结构表示
- **文件系统同步**: 所有操作都会实时反映到实际文件系统

### 文件和目录操作
- ✅ 创建新目录
- ✅ 创建新文件
- ✅ 重命名文件和目录
- ✅ 删除文件和目录（支持递归删除）
- ✅ 刷新视图

### 搜索功能
- ✅ 按名称搜索（支持大小写敏感/不敏感）
- ✅ 按文件扩展名搜索
- ✅ 按文件大小搜索
- ✅ 按内容类型搜索
- ✅ 正则表达式搜索支持

### 用户界面
- ✅ 直观的双面板布局
- ✅ 右键上下文菜单
- ✅ 工具栏快速操作
- ✅ 状态栏信息显示
- ✅ 键盘快捷键支持

## 系统要求

- **操作系统**: Linux (Ubuntu, Arch Linux, 或其他发行版)
- **Python**: 3.9 或更高版本
- **图形环境**: X11 或 Wayland
- **WSL2**: 支持（需要配置图形转发）

## 安装

### 1. 克隆或下载项目

```bash
# 如果使用 git
git clone <repository-url>
cd Directory-management

# 或者直接下载并解压项目文件
```

### 2. 创建虚拟环境（推荐）

```bash
python3 -m venv venv
source venv/bin/activate
```

### 3. 安装依赖项

```bash
pip install -r requirements.txt
```

### 4. 运行应用程序

```bash
python main.py
```

或者指定初始目录：

```bash
python main.py /path/to/directory
```

## WSL2 配置

如果您在 WSL2 中运行此应用程序，需要配置图形支持：

### 方法 1: 使用 WSLg (Windows 11)

Windows 11 用户可以直接使用 WSLg，无需额外配置。

### 方法 2: 使用 X11 转发

1. 安装 X11 服务器（如 VcXsrv 或 Xming）
2. 在 WSL2 中设置 DISPLAY 环境变量：

```bash
export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0
```

3. 将上述命令添加到 `~/.bashrc` 或 `~/.zshrc` 中以便自动设置。

## 使用说明

### 基本操作

1. **打开目录**: 使用 `Ctrl+O` 或点击工具栏的"打开目录"按钮
2. **导航**: 在左侧树视图中点击目录，右侧会显示其内容
3. **双击**: 在右侧列表中双击目录可以进入该目录

### 文件和目录操作

- **新建文件夹**: `Ctrl+Shift+N` 或右键菜单
- **新建文件**: `Ctrl+N` 或右键菜单
- **重命名**: 选中项目后按 `F2` 或右键菜单
- **删除**: 选中项目后按 `Delete` 或右键菜单
- **刷新**: `F5` 或右键菜单

### 搜索

1. 按 `Ctrl+F` 打开搜索对话框
2. 输入搜索关键词
3. 查看搜索结果

### 快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl+O` | 打开目录 |
| `Ctrl+N` | 新建文件 |
| `Ctrl+Shift+N` | 新建文件夹 |
| `F2` | 重命名 |
| `Delete` | 删除 |
| `Ctrl+F` | 搜索 |
| `F5` | 刷新 |
| `Ctrl+Q` | 退出 |

## 项目结构

```
Directory-management/
├── main.py                 # 应用程序入口点
├── requirements.txt        # Python 依赖项
├── README.md              # 项目说明文档
├── models/
│   └── node.py            # 节点数据模型
├── core/
│   ├── filesystem_manager.py  # 文件系统管理器
│   └── search_engine.py       # 搜索引擎
└── gui/
    ├── main_window.py     # 主窗口
    ├── tree_model.py      # 树视图模型
    ├── list_model.py      # 列表视图模型
    └── context_menu.py    # 上下文菜单管理器
```

## 技术特点

### 数据模型
- 使用内存树结构表示目录层次
- 支持文件和目录的统一表示
- 高效的搜索算法（深度优先搜索）

### 用户界面
- 基于 PyQt6 的现代 GUI
- 自定义模型-视图架构
- 响应式布局设计

### 文件系统集成
- 实时同步内存结构与磁盘
- 安全的文件操作（带确认对话框）
- 错误处理和用户反馈

## 故障排除

### 常见问题

1. **ImportError: No module named 'PyQt6'**
   ```bash
   pip install PyQt6
   ```

2. **图形界面无法显示（WSL2）**
   - 确保已安装 X11 服务器
   - 检查 DISPLAY 环境变量设置
   - 尝试使用 WSLg（Windows 11）

3. **权限错误**
   - 确保对目标目录有读写权限
   - 使用 `chmod` 命令调整权限

### 调试

启用详细输出：
```bash
python main.py --verbose
```

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

此项目使用 MIT 许可证。详见 LICENSE 文件。

## 作者

Augment Agent - 专业的 Python 桌面应用程序开发
