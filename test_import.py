#!/usr/bin/env python3
"""
导入测试脚本。

此脚本用于测试所有模块是否可以正确导入，而不启动 GUI。
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有模块导入。"""
    print("测试模块导入...")
    
    try:
        # 测试核心模块
        print("  导入 models.node...", end=" ")
        from models.node import Node, NodeType
        print("✓")
        
        print("  导入 core.filesystem_manager...", end=" ")
        from core.filesystem_manager import FileSystemManager
        print("✓")
        
        print("  导入 core.search_engine...", end=" ")
        from core.search_engine import SearchEngine, SearchCriteria, SearchType
        print("✓")
        
        # 测试 GUI 模块（可能需要显示环境）
        try:
            print("  导入 PyQt6...", end=" ")
            from PyQt6.QtWidgets import QApplication
            from PyQt6.QtCore import Qt
            print("✓")
            
            print("  导入 GUI 模块...", end=" ")
            from gui.tree_model import TreeModel
            from gui.list_model import ListModel
            from gui.context_menu import ContextMenuManager
            from gui.main_window import MainWindow
            print("✓")
            
        except ImportError as e:
            print(f"✗ (GUI 导入失败: {e})")
            return False
            
        print("\n所有模块导入成功！")
        return True
        
    except ImportError as e:
        print(f"✗ (导入失败: {e})")
        return False

def test_basic_functionality():
    """测试基本功能。"""
    print("\n测试基本功能...")
    
    try:
        # 测试节点创建
        print("  创建节点...", end=" ")
        from models.node import Node, NodeType
        root = Node("test_root", NodeType.DIRECTORY, "/tmp/test")
        child = Node("test_file.txt", NodeType.FILE, "/tmp/test/test_file.txt")
        root.add_child(child)
        print("✓")
        
        # 测试搜索
        print("  测试搜索...", end=" ")
        results = root.find_by_name("test", case_sensitive=False)
        assert len(results) >= 1
        print("✓")
        
        # 测试文件系统管理器
        print("  创建文件系统管理器...", end=" ")
        from core.filesystem_manager import FileSystemManager
        fs_manager = FileSystemManager()
        print("✓")
        
        # 测试搜索引擎
        print("  创建搜索引擎...", end=" ")
        from core.search_engine import SearchEngine
        search_engine = SearchEngine()
        print("✓")
        
        print("\n基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ (功能测试失败: {e})")
        return False

def main():
    """主函数。"""
    print("目录管理器 - 导入和功能测试")
    print("=" * 40)
    
    # 测试导入
    if not test_imports():
        print("\n导入测试失败！")
        return 1
        
    # 测试基本功能
    if not test_basic_functionality():
        print("\n功能测试失败！")
        return 1
        
    print("\n" + "=" * 40)
    print("所有测试通过！应用程序准备就绪。")
    print("\n要启动应用程序，请运行:")
    print("  python main.py")
    print("或:")
    print("  ./run.sh")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
