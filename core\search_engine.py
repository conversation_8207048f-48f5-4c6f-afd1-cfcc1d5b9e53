"""
搜索引擎模块。

此模块提供高级搜索功能，包括按名称、扩展名、大小等条件搜索文件和目录。
"""

import re
from typing import List, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass

from models.node import Node, NodeType


class SearchType(Enum):
    """搜索类型枚举。"""
    NAME = "name"
    EXTENSION = "extension"
    SIZE = "size"
    MODIFIED_TIME = "modified_time"
    CONTENT_TYPE = "content_type"


class SizeComparison(Enum):
    """大小比较类型。"""
    EQUAL = "equal"
    GREATER = "greater"
    LESS = "less"
    GREATER_EQUAL = "greater_equal"
    LESS_EQUAL = "less_equal"


@dataclass
class SearchCriteria:
    """搜索条件。"""
    search_type: SearchType
    value: Any
    case_sensitive: bool = False
    use_regex: bool = False
    size_comparison: Optional[SizeComparison] = None
    include_directories: bool = True
    include_files: bool = True


class SearchEngine:
    """
    搜索引擎类。
    
    提供各种搜索功能，支持多种搜索条件和算法。
    """
    
    def __init__(self):
        """初始化搜索引擎。"""
        pass
        
    def search(self, root_node: Node, criteria: SearchCriteria) -> List[Node]:
        """
        根据搜索条件搜索节点。
        
        Args:
            root_node: 搜索的根节点
            criteria: 搜索条件
            
        Returns:
            匹配的节点列表
        """
        results = []
        self._search_recursive(root_node, criteria, results)
        return results
        
    def _search_recursive(self, node: Node, criteria: SearchCriteria, results: List[Node]) -> None:
        """
        递归搜索节点。
        
        Args:
            node: 当前节点
            criteria: 搜索条件
            results: 结果列表
        """
        # 检查节点类型过滤
        if node.is_directory() and not criteria.include_directories:
            pass  # 不包含目录，但仍需要搜索其子节点
        elif node.is_file() and not criteria.include_files:
            pass  # 不包含文件
        else:
            # 检查是否匹配搜索条件
            if self._matches_criteria(node, criteria):
                results.append(node)
        
        # 递归搜索子节点
        if node.is_directory():
            for child in node.children:
                self._search_recursive(child, criteria, results)
                
    def _matches_criteria(self, node: Node, criteria: SearchCriteria) -> bool:
        """
        检查节点是否匹配搜索条件。
        
        Args:
            node: 要检查的节点
            criteria: 搜索条件
            
        Returns:
            是否匹配
        """
        if criteria.search_type == SearchType.NAME:
            return self._match_name(node, criteria)
        elif criteria.search_type == SearchType.EXTENSION:
            return self._match_extension(node, criteria)
        elif criteria.search_type == SearchType.SIZE:
            return self._match_size(node, criteria)
        elif criteria.search_type == SearchType.MODIFIED_TIME:
            return self._match_modified_time(node, criteria)
        elif criteria.search_type == SearchType.CONTENT_TYPE:
            return self._match_content_type(node, criteria)
        
        return False
        
    def _match_name(self, node: Node, criteria: SearchCriteria) -> bool:
        """匹配名称。"""
        search_value = str(criteria.value)
        node_name = node.name
        
        if not criteria.case_sensitive:
            search_value = search_value.lower()
            node_name = node_name.lower()
            
        if criteria.use_regex:
            try:
                pattern = re.compile(search_value, re.IGNORECASE if not criteria.case_sensitive else 0)
                return bool(pattern.search(node_name))
            except re.error:
                return False
        else:
            return search_value in node_name
            
    def _match_extension(self, node: Node, criteria: SearchCriteria) -> bool:
        """匹配扩展名。"""
        if node.is_directory():
            return False
            
        search_ext = str(criteria.value)
        if not search_ext.startswith('.'):
            search_ext = '.' + search_ext
            
        node_ext = ""
        if '.' in node.name:
            node_ext = '.' + node.name.split('.')[-1]
            
        if not criteria.case_sensitive:
            search_ext = search_ext.lower()
            node_ext = node_ext.lower()
            
        return node_ext == search_ext
        
    def _match_size(self, node: Node, criteria: SearchCriteria) -> bool:
        """匹配大小。"""
        try:
            node_size = node.get_size()
            target_size = int(criteria.value)
            
            if criteria.size_comparison == SizeComparison.EQUAL:
                return node_size == target_size
            elif criteria.size_comparison == SizeComparison.GREATER:
                return node_size > target_size
            elif criteria.size_comparison == SizeComparison.LESS:
                return node_size < target_size
            elif criteria.size_comparison == SizeComparison.GREATER_EQUAL:
                return node_size >= target_size
            elif criteria.size_comparison == SizeComparison.LESS_EQUAL:
                return node_size <= target_size
                
        except (ValueError, OSError):
            pass
            
        return False
        
    def _match_modified_time(self, node: Node, criteria: SearchCriteria) -> bool:
        """匹配修改时间。"""
        try:
            node_time = node.get_modified_time()
            target_time = float(criteria.value)
            
            if criteria.size_comparison == SizeComparison.EQUAL:
                return abs(node_time - target_time) < 1  # 1秒误差
            elif criteria.size_comparison == SizeComparison.GREATER:
                return node_time > target_time
            elif criteria.size_comparison == SizeComparison.LESS:
                return node_time < target_time
            elif criteria.size_comparison == SizeComparison.GREATER_EQUAL:
                return node_time >= target_time
            elif criteria.size_comparison == SizeComparison.LESS_EQUAL:
                return node_time <= target_time
                
        except (ValueError, OSError):
            pass
            
        return False
        
    def _match_content_type(self, node: Node, criteria: SearchCriteria) -> bool:
        """匹配内容类型。"""
        content_type = str(criteria.value).lower()
        
        if node.is_directory():
            return content_type == "directory" or content_type == "folder"
            
        # 根据扩展名判断文件类型
        ext = ""
        if '.' in node.name:
            ext = node.name.split('.')[-1].lower()
            
        type_mappings = {
            "text": ["txt", "md", "rst", "log"],
            "code": ["py", "js", "html", "css", "cpp", "c", "java", "php", "rb", "go"],
            "image": ["jpg", "jpeg", "png", "gif", "bmp", "svg", "ico"],
            "audio": ["mp3", "wav", "flac", "ogg", "m4a"],
            "video": ["mp4", "avi", "mkv", "mov", "wmv", "flv"],
            "document": ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"],
            "archive": ["zip", "rar", "7z", "tar", "gz", "bz2"]
        }
        
        for type_name, extensions in type_mappings.items():
            if content_type == type_name and ext in extensions:
                return True
                
        return False
        
    def search_by_name(self, root_node: Node, name: str, case_sensitive: bool = False, 
                      use_regex: bool = False) -> List[Node]:
        """
        按名称搜索。
        
        Args:
            root_node: 搜索的根节点
            name: 要搜索的名称
            case_sensitive: 是否区分大小写
            use_regex: 是否使用正则表达式
            
        Returns:
            匹配的节点列表
        """
        criteria = SearchCriteria(
            search_type=SearchType.NAME,
            value=name,
            case_sensitive=case_sensitive,
            use_regex=use_regex
        )
        return self.search(root_node, criteria)
        
    def search_by_extension(self, root_node: Node, extension: str) -> List[Node]:
        """
        按扩展名搜索。
        
        Args:
            root_node: 搜索的根节点
            extension: 要搜索的扩展名
            
        Returns:
            匹配的节点列表
        """
        criteria = SearchCriteria(
            search_type=SearchType.EXTENSION,
            value=extension,
            include_directories=False  # 只搜索文件
        )
        return self.search(root_node, criteria)
        
    def search_by_size(self, root_node: Node, size: int, 
                      comparison: SizeComparison = SizeComparison.EQUAL) -> List[Node]:
        """
        按大小搜索。
        
        Args:
            root_node: 搜索的根节点
            size: 目标大小（字节）
            comparison: 比较类型
            
        Returns:
            匹配的节点列表
        """
        criteria = SearchCriteria(
            search_type=SearchType.SIZE,
            value=size,
            size_comparison=comparison
        )
        return self.search(root_node, criteria)
        
    def search_by_content_type(self, root_node: Node, content_type: str) -> List[Node]:
        """
        按内容类型搜索。
        
        Args:
            root_node: 搜索的根节点
            content_type: 内容类型（如 "image", "text", "code" 等）
            
        Returns:
            匹配的节点列表
        """
        criteria = SearchCriteria(
            search_type=SearchType.CONTENT_TYPE,
            value=content_type
        )
        return self.search(root_node, criteria)
