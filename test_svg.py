#!/usr/bin/env python3
"""测试 SVG 支持"""

try:
    from PyQt6.QtSvg import QSvgRenderer
    from PyQt6.QtGui import QPixmap, QPainter
    from PyQt6.QtCore import QByteArray, QSize
    print("✓ SVG 支持可用")
    
    # 测试创建简单的 SVG
    svg_data = """
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 2L2 8L8 14L14 8L8 2Z" fill="#ffffff"/>
    </svg>
    """
    
    svg_bytes = QByteArray(svg_data.encode('utf-8'))
    renderer = QSvgRenderer(svg_bytes)
    
    pixmap = QPixmap(QSize(16, 16))
    pixmap.fill(0)  # 透明背景
    
    painter = QPainter(pixmap)
    renderer.render(painter)
    painter.end()
    
    print("✓ SVG 渲染测试成功")
    
except ImportError as e:
    print(f"✗ SVG 支持不可用: {e}")
except Exception as e:
    print(f"✗ SVG 测试失败: {e}")
