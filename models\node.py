"""
Core data model for the directory manager application.

This module contains the Node class that represents both files and directories
in an in-memory tree structure.
"""

import os
from typing import List, Optional, Dict, Any
from pathlib import Path
from enum import Enum


class NodeType(Enum):
    """Enumeration for node types."""
    FILE = "file"
    DIRECTORY = "directory"


class Node:
    """
    Represents a node in the directory tree structure.
    
    This class can represent both files (leaf nodes) and directories (branch nodes).
    It maintains the hierarchical structure and provides methods for tree operations.
    """
    
    def __init__(self, name: str, node_type: NodeType, path: str, parent: Optional['Node'] = None):
        """
        Initialize a new node.
        
        Args:
            name: The name of the file or directory
            node_type: Whether this is a file or directory
            path: The full filesystem path
            parent: The parent node (None for root)
        """
        self.name = name
        self.node_type = node_type
        self.path = path
        self.parent = parent
        self.children: List['Node'] = []
        self._size: Optional[int] = None
        self._modified_time: Optional[float] = None
        
    def add_child(self, child: 'Node') -> None:
        """Add a child node to this directory."""
        if self.node_type != NodeType.DIRECTORY:
            raise ValueError("Cannot add children to a file node")
        
        child.parent = self
        self.children.append(child)
        
    def remove_child(self, child: 'Node') -> None:
        """Remove a child node from this directory."""
        if child in self.children:
            self.children.remove(child)
            child.parent = None
            
    def get_child_by_name(self, name: str) -> Optional['Node']:
        """Get a child node by name."""
        for child in self.children:
            if child.name == name:
                return child
        return None
        
    def is_directory(self) -> bool:
        """Check if this node represents a directory."""
        return self.node_type == NodeType.DIRECTORY
        
    def is_file(self) -> bool:
        """Check if this node represents a file."""
        return self.node_type == NodeType.FILE
        
    def get_full_path(self) -> str:
        """Get the full filesystem path for this node."""
        return self.path
        
    def get_relative_path(self, root_path: str) -> str:
        """Get the path relative to a root directory."""
        try:
            return os.path.relpath(self.path, root_path)
        except ValueError:
            return self.path
            
    def get_size(self) -> int:
        """Get the size of the file or directory."""
        if self._size is None:
            try:
                if self.is_file():
                    self._size = os.path.getsize(self.path)
                else:
                    # For directories, calculate total size of all contents
                    total_size = 0
                    for child in self.children:
                        total_size += child.get_size()
                    self._size = total_size
            except (OSError, IOError):
                self._size = 0
        return self._size
        
    def get_modified_time(self) -> float:
        """Get the last modified time of the file or directory."""
        if self._modified_time is None:
            try:
                self._modified_time = os.path.getmtime(self.path)
            except (OSError, IOError):
                self._modified_time = 0.0
        return self._modified_time
        
    def invalidate_cache(self) -> None:
        """Invalidate cached size and time information."""
        self._size = None
        self._modified_time = None
        
    def find_by_name(self, name: str, case_sensitive: bool = True) -> List['Node']:
        """
        Search for nodes by name using depth-first search.
        
        Args:
            name: The name to search for
            case_sensitive: Whether the search should be case sensitive
            
        Returns:
            List of nodes matching the search criteria
        """
        results = []
        search_name = name if case_sensitive else name.lower()
        node_name = self.name if case_sensitive else self.name.lower()
        
        # Check if current node matches
        if search_name in node_name:
            results.append(self)
            
        # Recursively search children
        for child in self.children:
            results.extend(child.find_by_name(name, case_sensitive))
            
        return results
        
    def find_by_extension(self, extension: str) -> List['Node']:
        """
        Find all files with a specific extension.
        
        Args:
            extension: The file extension to search for (with or without dot)
            
        Returns:
            List of file nodes with the specified extension
        """
        results = []
        
        # Normalize extension
        if not extension.startswith('.'):
            extension = '.' + extension
            
        # Check current node if it's a file
        if self.is_file() and self.name.lower().endswith(extension.lower()):
            results.append(self)
            
        # Recursively search children
        for child in self.children:
            results.extend(child.find_by_extension(extension))
            
        return results
        
    def get_tree_structure(self, indent: int = 0) -> str:
        """
        Get a string representation of the tree structure.
        
        Args:
            indent: Current indentation level
            
        Returns:
            String representation of the tree
        """
        result = "  " * indent + f"{'📁' if self.is_directory() else '📄'} {self.name}\n"
        
        for child in sorted(self.children, key=lambda x: (x.is_file(), x.name.lower())):
            result += child.get_tree_structure(indent + 1)
            
        return result
        
    def __str__(self) -> str:
        """String representation of the node."""
        return f"{self.node_type.value}: {self.name}"
        
    def __repr__(self) -> str:
        """Detailed string representation of the node."""
        return f"Node(name='{self.name}', type={self.node_type}, path='{self.path}')"
