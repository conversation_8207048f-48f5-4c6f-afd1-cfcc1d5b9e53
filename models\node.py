"""
目录管理应用程序的核心数据模型。

此模块包含 Node 类，用于在内存树结构中表示文件和目录。
"""

import os
from typing import List, Optional, Dict, Any
from pathlib import Path
from enum import Enum


class NodeType(Enum):
    """节点类型枚举。"""
    FILE = "file"
    DIRECTORY = "directory"


class Node:
    """
    表示目录树结构中的一个节点。

    此类可以表示文件（叶节点）和目录（分支节点）。
    它维护层次结构并提供树操作方法。
    """

    def __init__(self, name: str, node_type: NodeType, path: str, parent: Optional['Node'] = None):
        """
        初始化一个新节点。

        Args:
            name: 文件或目录的名称
            node_type: 是文件还是目录
            path: 完整的文件系统路径
            parent: 父节点（根节点为 None）
        """
        self.name = name
        self.node_type = node_type
        self.path = path
        self.parent = parent
        self.children: List['Node'] = []
        self._size: Optional[int] = None
        self._modified_time: Optional[float] = None

    def add_child(self, child: 'Node') -> None:
        """向此目录添加子节点。"""
        if self.node_type != NodeType.DIRECTORY:
            raise ValueError("无法向文件节点添加子节点")

        child.parent = self
        self.children.append(child)

    def remove_child(self, child: 'Node') -> None:
        """从此目录中移除子节点。"""
        if child in self.children:
            self.children.remove(child)
            child.parent = None

    def get_child_by_name(self, name: str) -> Optional['Node']:
        """根据名称获取子节点。"""
        for child in self.children:
            if child.name == name:
                return child
        return None

    def is_directory(self) -> bool:
        """检查此节点是否表示目录。"""
        return self.node_type == NodeType.DIRECTORY

    def is_file(self) -> bool:
        """检查此节点是否表示文件。"""
        return self.node_type == NodeType.FILE

    def get_full_path(self) -> str:
        """获取此节点的完整文件系统路径。"""
        return self.path

    def get_relative_path(self, root_path: str) -> str:
        """获取相对于根目录的路径。"""
        try:
            return os.path.relpath(self.path, root_path)
        except ValueError:
            return self.path

    def get_size(self) -> int:
        """获取文件或目录的大小。"""
        if self._size is None:
            try:
                if self.is_file():
                    self._size = os.path.getsize(self.path)
                else:
                    # 对于目录，计算所有内容的总大小
                    total_size = 0
                    for child in self.children:
                        total_size += child.get_size()
                    self._size = total_size
            except (OSError, IOError):
                self._size = 0
        return self._size

    def get_modified_time(self) -> float:
        """获取文件或目录的最后修改时间。"""
        if self._modified_time is None:
            try:
                self._modified_time = os.path.getmtime(self.path)
            except (OSError, IOError):
                self._modified_time = 0.0
        return self._modified_time

    def invalidate_cache(self) -> None:
        """使缓存的大小和时间信息失效。"""
        self._size = None
        self._modified_time = None

    def find_by_name(self, name: str, case_sensitive: bool = True) -> List['Node']:
        """
        使用深度优先搜索按名称搜索节点。

        Args:
            name: 要搜索的名称
            case_sensitive: 搜索是否区分大小写

        Returns:
            匹配搜索条件的节点列表
        """
        results = []
        search_name = name if case_sensitive else name.lower()
        node_name = self.name if case_sensitive else self.name.lower()

        # 检查当前节点是否匹配
        if search_name in node_name:
            results.append(self)

        # 递归搜索子节点
        for child in self.children:
            results.extend(child.find_by_name(name, case_sensitive))

        return results

    def find_by_extension(self, extension: str) -> List['Node']:
        """
        查找具有特定扩展名的所有文件。

        Args:
            extension: 要搜索的文件扩展名（带或不带点）

        Returns:
            具有指定扩展名的文件节点列表
        """
        results = []

        # 规范化扩展名
        if not extension.startswith('.'):
            extension = '.' + extension

        # 如果当前节点是文件，检查是否匹配
        if self.is_file() and self.name.lower().endswith(extension.lower()):
            results.append(self)

        # 递归搜索子节点
        for child in self.children:
            results.extend(child.find_by_extension(extension))

        return results

    def get_tree_structure(self, indent: int = 0) -> str:
        """
        获取树结构的字符串表示。

        Args:
            indent: 当前缩进级别

        Returns:
            树的字符串表示
        """
        result = "  " * indent + f"{'📁' if self.is_directory() else '📄'} {self.name}\n"

        for child in sorted(self.children, key=lambda x: (x.is_file(), x.name.lower())):
            result += child.get_tree_structure(indent + 1)

        return result

    def __str__(self) -> str:
        """节点的字符串表示。"""
        return f"{self.node_type.value}: {self.name}"

    def __repr__(self) -> str:
        """节点的详细字符串表示。"""
        return f"Node(name='{self.name}', type={self.node_type}, path='{self.path}')"
