#!/bin/bash

# 目录管理器启动脚本
# 
# 此脚本用于启动目录管理器应用程序，并进行必要的环境检查。

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查 Python 版本
check_python() {
    print_info "检查 Python 版本..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "未找到 python3 命令"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    required_version="3.9"
    
    if python3 -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)"; then
        print_success "Python 版本: $python_version (满足要求 >= $required_version)"
    else
        print_error "Python 版本 $python_version 不满足要求 (>= $required_version)"
        exit 1
    fi
}

# 检查虚拟环境
check_venv() {
    if [[ "$VIRTUAL_ENV" != "" ]]; then
        print_success "已激活虚拟环境: $VIRTUAL_ENV"
    else
        print_warning "未检测到虚拟环境"
        print_info "建议创建并激活虚拟环境:"
        print_info "  python3 -m venv venv"
        print_info "  source venv/bin/activate"
        echo
    fi
}

# 检查依赖项
check_dependencies() {
    print_info "检查依赖项..."
    
    if python3 -c "import PyQt6" 2>/dev/null; then
        pyqt_version=$(python3 -c "from PyQt6.QtCore import QT_VERSION_STR; print(QT_VERSION_STR)")
        print_success "PyQt6 已安装 (版本: $pyqt_version)"
    else
        print_error "PyQt6 未安装"
        print_info "正在安装依赖项..."
        pip install -r requirements.txt
        print_success "依赖项安装完成"
    fi
}

# 检查图形环境
check_display() {
    print_info "检查图形环境..."
    
    if [[ -n "$DISPLAY" ]]; then
        print_success "检测到 X11 显示: $DISPLAY"
    elif [[ -n "$WAYLAND_DISPLAY" ]]; then
        print_success "检测到 Wayland 显示: $WAYLAND_DISPLAY"
    else
        print_warning "未检测到图形显示环境"
        
        # 检查是否在 WSL 中
        if grep -qi microsoft /proc/version 2>/dev/null; then
            print_info "检测到 WSL 环境"
            print_info "请确保已配置图形转发或使用 WSLg"
            
            # 尝试设置 DISPLAY
            if [[ -z "$DISPLAY" ]]; then
                nameserver=$(grep nameserver /etc/resolv.conf | awk '{print $2}' | head -1)
                if [[ -n "$nameserver" ]]; then
                    export DISPLAY="$nameserver:0"
                    print_info "尝试设置 DISPLAY=$DISPLAY"
                fi
            fi
        fi
    fi
}

# 启动应用程序
start_app() {
    print_info "启动目录管理器..."
    
    # 传递所有命令行参数给 Python 脚本
    python3 main.py "$@"
}

# 主函数
main() {
    echo "========================================"
    echo "        目录管理器启动脚本"
    echo "========================================"
    echo
    
    # 检查当前目录
    if [[ ! -f "main.py" ]]; then
        print_error "未找到 main.py 文件"
        print_info "请确保在项目根目录中运行此脚本"
        exit 1
    fi
    
    # 执行检查
    check_python
    check_venv
    check_dependencies
    check_display
    
    echo
    print_info "所有检查完成，准备启动应用程序..."
    echo
    
    # 启动应用程序
    start_app "$@"
}

# 显示帮助信息
show_help() {
    echo "目录管理器启动脚本"
    echo
    echo "用法: $0 [选项] [目录路径]"
    echo
    echo "选项:"
    echo "  -h, --help    显示此帮助信息"
    echo
    echo "参数:"
    echo "  目录路径      可选，指定要打开的初始目录"
    echo
    echo "示例:"
    echo "  $0"
    echo "  $0 /home/<USER>/documents"
    echo "  $0 ."
}

# 处理命令行参数
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_help
    exit 0
fi

# 运行主函数
main "$@"
