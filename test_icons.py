#!/usr/bin/env python3
"""测试图标管理器"""

try:
    print("测试图标管理器...")
    from gui.icons import icon_manager
    print("✓ 图标管理器导入成功")
    
    # 测试获取图标
    icon = icon_manager.get_icon("folder", 16, "#ffffff")
    print("✓ 获取文件夹图标成功")
    
    # 测试文件图标
    file_icon = icon_manager.get_file_icon("test.py", 16)
    print("✓ 获取文件图标成功")
    
    print("所有图标测试通过！")
    
except Exception as e:
    print(f"✗ 图标测试失败: {e}")
    import traceback
    traceback.print_exc()
