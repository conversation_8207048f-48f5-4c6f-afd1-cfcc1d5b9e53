"""
PyQt6 树模型模块。

此模块包含用于在 QTreeView 中显示目录结构的自定义模型。
"""

from PyQt6.QtCore import QAbstractItemModel, QModelIndex, Qt, QVariant
from PyQt6.QtGui import QIcon, QStandardItem
from typing import Any, Optional

from models.node import Node, NodeType
from gui.icons import icon_manager


class TreeModel(QAbstractItemModel):
    """
    自定义树模型，用于在 QTreeView 中显示目录结构。
    """
    
    def __init__(self, root_node: Optional[Node] = None):
        """
        初始化树模型。
        
        Args:
            root_node: 根节点
        """
        super().__init__()
        self._root_node = root_node
        
    def set_root_node(self, root_node: Node) -> None:
        """
        设置根节点并刷新模型。
        
        Args:
            root_node: 新的根节点
        """
        self.beginResetModel()
        self._root_node = root_node
        self.endResetModel()
        
    def get_node(self, index: QModelIndex) -> Optional[Node]:
        """
        从模型索引获取节点。
        
        Args:
            index: 模型索引
            
        Returns:
            对应的节点，如果索引无效则返回 None
        """
        if not index.isValid():
            return self._root_node
        return index.internalPointer()
        
    def index(self, row: int, column: int, parent: QModelIndex = QModelIndex()) -> QModelIndex:
        """
        创建模型索引。
        
        Args:
            row: 行号
            column: 列号
            parent: 父索引
            
        Returns:
            模型索引
        """
        if not self.hasIndex(row, column, parent):
            return QModelIndex()
            
        parent_node = self.get_node(parent)
        if not parent_node:
            return QModelIndex()
            
        if row < len(parent_node.children):
            child_node = parent_node.children[row]
            return self.createIndex(row, column, child_node)
            
        return QModelIndex()
        
    def parent(self, index: QModelIndex) -> QModelIndex:
        """
        获取父索引。
        
        Args:
            index: 子索引
            
        Returns:
            父索引
        """
        if not index.isValid():
            return QModelIndex()
            
        child_node = self.get_node(index)
        if not child_node or not child_node.parent:
            return QModelIndex()
            
        parent_node = child_node.parent
        if parent_node == self._root_node:
            return QModelIndex()
            
        # 找到父节点在其父节点中的位置
        grandparent_node = parent_node.parent
        if grandparent_node:
            row = grandparent_node.children.index(parent_node)
            return self.createIndex(row, 0, parent_node)
            
        return QModelIndex()
        
    def rowCount(self, parent: QModelIndex = QModelIndex()) -> int:
        """
        获取行数。
        
        Args:
            parent: 父索引
            
        Returns:
            子项数量
        """
        parent_node = self.get_node(parent)
        if not parent_node:
            return 0
        return len(parent_node.children)
        
    def columnCount(self, parent: QModelIndex = QModelIndex()) -> int:
        """
        获取列数。
        
        Args:
            parent: 父索引
            
        Returns:
            列数（固定为1）
        """
        return 1
        
    def data(self, index: QModelIndex, role: int = Qt.ItemDataRole.DisplayRole) -> Any:
        """
        获取数据。
        
        Args:
            index: 模型索引
            role: 数据角色
            
        Returns:
            对应角色的数据
        """
        if not index.isValid():
            return QVariant()
            
        node = self.get_node(index)
        if not node:
            return QVariant()
            
        if role == Qt.ItemDataRole.DisplayRole:
            return node.name
        elif role == Qt.ItemDataRole.ToolTipRole:
            return node.path
        elif role == Qt.ItemDataRole.DecorationRole:
            # 返回 Plasma 风格图标
            if node.is_directory():
                return icon_manager.get_icon("folder", 16, "#3daee9")  # Plasma 蓝色目录图标
            else:
                return icon_manager.get_file_icon(node.name, 16)  # 根据文件类型的图标
                
        return QVariant()
        
    def flags(self, index: QModelIndex) -> Qt.ItemFlag:
        """
        获取项目标志。
        
        Args:
            index: 模型索引
            
        Returns:
            项目标志
        """
        if not index.isValid():
            return Qt.ItemFlag.NoItemFlags
            
        return (Qt.ItemFlag.ItemIsEnabled | 
                Qt.ItemFlag.ItemIsSelectable)
                
    def headerData(self, section: int, orientation: Qt.Orientation, 
                   role: int = Qt.ItemDataRole.DisplayRole) -> Any:
        """
        获取标题数据。
        
        Args:
            section: 节号
            orientation: 方向
            role: 数据角色
            
        Returns:
            标题数据
        """
        if (orientation == Qt.Orientation.Horizontal and 
            role == Qt.ItemDataRole.DisplayRole and 
            section == 0):
            return "目录结构"
        return QVariant()
        
    def refresh_node(self, node: Node) -> None:
        """
        刷新指定节点。
        
        Args:
            node: 要刷新的节点
        """
        index = self.get_index_for_node(node)
        if index.isValid():
            self.dataChanged.emit(index, index)
            
    def get_index_for_node(self, target_node: Node) -> QModelIndex:
        """
        获取节点对应的模型索引。
        
        Args:
            target_node: 目标节点
            
        Returns:
            对应的模型索引
        """
        if target_node == self._root_node:
            return QModelIndex()
            
        # 构建从根到目标节点的路径
        path = []
        current = target_node
        while current and current != self._root_node:
            if current.parent:
                row = current.parent.children.index(current)
                path.insert(0, row)
            current = current.parent
            
        # 根据路径创建索引
        index = QModelIndex()
        for row in path:
            index = self.index(row, 0, index)
            
        return index
        
    def add_node(self, parent_node: Node, new_node: Node) -> None:
        """
        添加新节点到模型。
        
        Args:
            parent_node: 父节点
            new_node: 新节点
        """
        parent_index = self.get_index_for_node(parent_node)
        row = len(parent_node.children) - 1  # 新节点已经添加到父节点的children中
        
        self.beginInsertRows(parent_index, row, row)
        self.endInsertRows()
        
    def remove_node(self, node: Node) -> None:
        """
        从模型中移除节点。
        
        Args:
            node: 要移除的节点
        """
        if not node.parent:
            return
            
        parent_index = self.get_index_for_node(node.parent)
        row = node.parent.children.index(node)
        
        self.beginRemoveRows(parent_index, row, row)
        self.endRemoveRows()
        
    def update_node(self, node: Node) -> None:
        """
        更新节点数据。
        
        Args:
            node: 要更新的节点
        """
        index = self.get_index_for_node(node)
        if index.isValid():
            self.dataChanged.emit(index, index)
