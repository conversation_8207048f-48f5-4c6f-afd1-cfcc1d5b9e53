# 目录管理器应用程序依赖项
# 
# 此文件列出了运行目录管理器应用程序所需的 Python 包。
# 
# 安装方法:
#   pip install -r requirements.txt
# 
# 或者在虚拟环境中:
#   python -m venv venv
#   source venv/bin/activate  # Linux/macOS
#   pip install -r requirements.txt

# PyQt6 - 主要的 GUI 框架
PyQt6>=6.4.0

# PyQt6 SVG 支持 - 用于现代化图标
PyQt6-SVG>=6.4.0

# 可选依赖项（用于增强功能）
# 如果需要更好的图标支持，可以取消注释以下行：
# Pillow>=9.0.0

# 开发依赖项（可选）
# 如果需要进行开发或测试，可以取消注释以下行：
# pytest>=7.0.0
# pytest-qt>=4.0.0
# black>=22.0.0
# flake8>=4.0.0
# mypy>=0.950
