@echo off
REM 目录管理器启动脚本 (Windows)
REM 
REM 此脚本用于在 Windows 环境下启动目录管理器应用程序

setlocal enabledelayedexpansion

echo ========================================
echo         目录管理器启动脚本
echo ========================================
echo.

REM 检查是否存在虚拟环境
if not exist "venv\Scripts\python.exe" (
    echo [错误] 未找到虚拟环境
    echo 请先创建虚拟环境:
    echo   python -m venv venv
    echo   venv\Scripts\activate
    echo   pip install -r requirements.txt
    pause
    exit /b 1
)

REM 检查是否存在 main.py
if not exist "main.py" (
    echo [错误] 未找到 main.py 文件
    echo 请确保在项目根目录中运行此脚本
    pause
    exit /b 1
)

echo [信息] 检查虚拟环境... 完成
echo [信息] 启动目录管理器...
echo.

REM 使用虚拟环境中的 Python 启动应用程序
venv\Scripts\python.exe main.py %*

REM 如果应用程序异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo [错误] 应用程序异常退出
    pause
)

endlocal
