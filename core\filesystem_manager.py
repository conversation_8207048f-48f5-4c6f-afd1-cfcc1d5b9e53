"""
文件系统管理器模块。

此模块负责在内存树结构和实际文件系统之间进行交互。
它提供加载目录结构、创建、删除和重命名文件/目录的功能。
"""

import os
import shutil
from typing import Optional, List
from pathlib import Path

from models.node import Node, NodeType


class FileSystemManager:
    """
    文件系统管理器类。
    
    负责管理内存树结构与实际文件系统之间的同步。
    """
    
    def __init__(self):
        """初始化文件系统管理器。"""
        self.root_node: Optional[Node] = None
        self.root_path: str = ""
        
    def load_directory_tree(self, root_path: str) -> Node:
        """
        从文件系统加载目录树到内存结构中。
        
        Args:
            root_path: 要加载的根目录路径
            
        Returns:
            表示根目录的节点
            
        Raises:
            OSError: 如果路径不存在或无法访问
        """
        if not os.path.exists(root_path):
            raise OSError(f"路径不存在: {root_path}")
            
        if not os.path.isdir(root_path):
            raise OSError(f"路径不是目录: {root_path}")
            
        self.root_path = os.path.abspath(root_path)
        root_name = os.path.basename(self.root_path) or self.root_path
        
        self.root_node = Node(
            name=root_name,
            node_type=NodeType.DIRECTORY,
            path=self.root_path
        )
        
        self._load_directory_recursive(self.root_node)
        return self.root_node
        
    def _load_directory_recursive(self, parent_node: Node) -> None:
        """
        递归加载目录内容。
        
        Args:
            parent_node: 父目录节点
        """
        try:
            entries = os.listdir(parent_node.path)
            entries.sort()  # 按字母顺序排序
            
            for entry in entries:
                # 跳过隐藏文件（以点开头的文件）
                if entry.startswith('.'):
                    continue
                    
                entry_path = os.path.join(parent_node.path, entry)
                
                try:
                    if os.path.isdir(entry_path):
                        # 创建目录节点
                        dir_node = Node(
                            name=entry,
                            node_type=NodeType.DIRECTORY,
                            path=entry_path,
                            parent=parent_node
                        )
                        parent_node.add_child(dir_node)
                        
                        # 递归加载子目录
                        self._load_directory_recursive(dir_node)
                        
                    elif os.path.isfile(entry_path):
                        # 创建文件节点
                        file_node = Node(
                            name=entry,
                            node_type=NodeType.FILE,
                            path=entry_path,
                            parent=parent_node
                        )
                        parent_node.add_child(file_node)
                        
                except (OSError, PermissionError):
                    # 跳过无法访问的文件或目录
                    continue
                    
        except (OSError, PermissionError):
            # 无法读取目录内容
            pass
            
    def create_directory(self, parent_node: Node, name: str) -> Node:
        """
        创建新目录。
        
        Args:
            parent_node: 父目录节点
            name: 新目录名称
            
        Returns:
            新创建的目录节点
            
        Raises:
            ValueError: 如果父节点不是目录或名称无效
            OSError: 如果创建目录失败
        """
        if not parent_node.is_directory():
            raise ValueError("父节点必须是目录")
            
        if not name or '/' in name or '\\' in name:
            raise ValueError("目录名称无效")
            
        # 检查是否已存在同名项目
        if parent_node.get_child_by_name(name):
            raise ValueError(f"已存在名为 '{name}' 的项目")
            
        new_path = os.path.join(parent_node.path, name)
        
        try:
            os.makedirs(new_path, exist_ok=False)
        except OSError as e:
            raise OSError(f"创建目录失败: {e}")
            
        # 创建新节点并添加到树中
        new_node = Node(
            name=name,
            node_type=NodeType.DIRECTORY,
            path=new_path,
            parent=parent_node
        )
        parent_node.add_child(new_node)
        
        return new_node
        
    def create_file(self, parent_node: Node, name: str) -> Node:
        """
        创建新文件。
        
        Args:
            parent_node: 父目录节点
            name: 新文件名称
            
        Returns:
            新创建的文件节点
            
        Raises:
            ValueError: 如果父节点不是目录或名称无效
            OSError: 如果创建文件失败
        """
        if not parent_node.is_directory():
            raise ValueError("父节点必须是目录")
            
        if not name or '/' in name or '\\' in name:
            raise ValueError("文件名称无效")
            
        # 检查是否已存在同名项目
        if parent_node.get_child_by_name(name):
            raise ValueError(f"已存在名为 '{name}' 的项目")
            
        new_path = os.path.join(parent_node.path, name)
        
        try:
            # 创建空文件
            with open(new_path, 'w', encoding='utf-8') as f:
                pass
        except OSError as e:
            raise OSError(f"创建文件失败: {e}")
            
        # 创建新节点并添加到树中
        new_node = Node(
            name=name,
            node_type=NodeType.FILE,
            path=new_path,
            parent=parent_node
        )
        parent_node.add_child(new_node)
        
        return new_node
        
    def delete_node(self, node: Node) -> None:
        """
        删除节点（文件或目录）。
        
        Args:
            node: 要删除的节点
            
        Raises:
            ValueError: 如果尝试删除根节点
            OSError: 如果删除失败
        """
        if node == self.root_node:
            raise ValueError("无法删除根节点")
            
        try:
            if node.is_directory():
                # 删除目录及其所有内容
                shutil.rmtree(node.path)
            else:
                # 删除文件
                os.remove(node.path)
                
            # 从树中移除节点
            if node.parent:
                node.parent.remove_child(node)
                
        except OSError as e:
            raise OSError(f"删除失败: {e}")
            
    def rename_node(self, node: Node, new_name: str) -> None:
        """
        重命名节点。
        
        Args:
            node: 要重命名的节点
            new_name: 新名称
            
        Raises:
            ValueError: 如果新名称无效或已存在
            OSError: 如果重命名失败
        """
        if not new_name or '/' in new_name or '\\' in new_name:
            raise ValueError("名称无效")
            
        if node.name == new_name:
            return  # 名称没有改变
            
        # 检查父目录中是否已存在同名项目
        if node.parent and node.parent.get_child_by_name(new_name):
            raise ValueError(f"已存在名为 '{new_name}' 的项目")
            
        old_path = node.path
        new_path = os.path.join(os.path.dirname(old_path), new_name)
        
        try:
            os.rename(old_path, new_path)
            
            # 更新节点信息
            node.name = new_name
            node.path = new_path
            
            # 如果是目录，需要递归更新所有子节点的路径
            if node.is_directory():
                self._update_paths_recursive(node)
                
        except OSError as e:
            raise OSError(f"重命名失败: {e}")
            
    def _update_paths_recursive(self, node: Node) -> None:
        """
        递归更新节点及其子节点的路径。
        
        Args:
            node: 要更新的节点
        """
        for child in node.children:
            child.path = os.path.join(node.path, child.name)
            if child.is_directory():
                self._update_paths_recursive(child)
                
    def refresh_node(self, node: Node) -> None:
        """
        刷新节点，重新从文件系统加载其内容。
        
        Args:
            node: 要刷新的节点
        """
        if not node.is_directory():
            return
            
        # 清除现有子节点
        node.children.clear()
        
        # 重新加载目录内容
        self._load_directory_recursive(node)
        
        # 使缓存失效
        node.invalidate_cache()
        
    def get_root_node(self) -> Optional[Node]:
        """获取根节点。"""
        return self.root_node
        
    def get_root_path(self) -> str:
        """获取根路径。"""
        return self.root_path
