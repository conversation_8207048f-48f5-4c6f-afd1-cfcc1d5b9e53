"""
PyQt6 列表模型模块。

此模块包含用于在 QListView 中显示目录内容的自定义模型。
"""

from PyQt6.QtCore import QAbstractListModel, QModelIndex, Qt, QVariant
from PyQt6.QtGui import QIcon
from typing import Any, Optional, List
import os
from datetime import datetime

from models.node import Node, NodeType
from gui.icons import icon_manager


class ListModel(QAbstractListModel):
    """
    自定义列表模型，用于在 QListView 中显示目录内容。
    """
    
    def __init__(self, parent_node: Optional[Node] = None):
        """
        初始化列表模型。
        
        Args:
            parent_node: 要显示内容的目录节点
        """
        super().__init__()
        self._parent_node = parent_node
        self._items: List[Node] = []
        self._update_items()
        
    def set_parent_node(self, parent_node: Optional[Node]) -> None:
        """
        设置父节点并刷新模型。
        
        Args:
            parent_node: 新的父节点
        """
        self.beginResetModel()
        self._parent_node = parent_node
        self._update_items()
        self.endResetModel()
        
    def _update_items(self) -> None:
        """更新项目列表。"""
        self._items.clear()
        if self._parent_node and self._parent_node.is_directory():
            # 按类型和名称排序：目录在前，然后按名称排序
            self._items = sorted(
                self._parent_node.children,
                key=lambda x: (x.is_file(), x.name.lower())
            )
            
    def get_node(self, index: QModelIndex) -> Optional[Node]:
        """
        从模型索引获取节点。
        
        Args:
            index: 模型索引
            
        Returns:
            对应的节点，如果索引无效则返回 None
        """
        if not index.isValid() or index.row() >= len(self._items):
            return None
        return self._items[index.row()]
        
    def rowCount(self, parent: QModelIndex = QModelIndex()) -> int:
        """
        获取行数。
        
        Args:
            parent: 父索引（在列表模型中未使用）
            
        Returns:
            项目数量
        """
        return len(self._items)
        
    def data(self, index: QModelIndex, role: int = Qt.ItemDataRole.DisplayRole) -> Any:
        """
        获取数据。
        
        Args:
            index: 模型索引
            role: 数据角色
            
        Returns:
            对应角色的数据
        """
        if not index.isValid() or index.row() >= len(self._items):
            return QVariant()
            
        node = self._items[index.row()]
        
        if role == Qt.ItemDataRole.DisplayRole:
            return node.name
        elif role == Qt.ItemDataRole.ToolTipRole:
            # 显示详细信息作为工具提示
            tooltip = f"名称: {node.name}\n"
            tooltip += f"路径: {node.path}\n"
            tooltip += f"类型: {'目录' if node.is_directory() else '文件'}\n"
            
            try:
                size = node.get_size()
                if node.is_file():
                    tooltip += f"大小: {self._format_size(size)}\n"
                else:
                    tooltip += f"项目数: {len(node.children)}\n"
                    
                mod_time = node.get_modified_time()
                if mod_time > 0:
                    dt = datetime.fromtimestamp(mod_time)
                    tooltip += f"修改时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}"
            except (OSError, IOError):
                pass
                
            return tooltip
        elif role == Qt.ItemDataRole.DecorationRole:
            # 返回 Plasma 风格图标
            if node.is_directory():
                return icon_manager.get_icon("folder", 20, "#3daee9")  # Plasma 蓝色目录图标
            else:
                return icon_manager.get_file_icon(node.name, 20)  # 根据文件类型的彩色图标
        elif role == Qt.ItemDataRole.UserRole:
            # 存储节点对象本身
            return node
            
        return QVariant()
        
    def flags(self, index: QModelIndex) -> Qt.ItemFlag:
        """
        获取项目标志。
        
        Args:
            index: 模型索引
            
        Returns:
            项目标志
        """
        if not index.isValid():
            return Qt.ItemFlag.NoItemFlags
            
        return (Qt.ItemFlag.ItemIsEnabled | 
                Qt.ItemFlag.ItemIsSelectable)
                
    def _format_size(self, size: int) -> str:
        """
        格式化文件大小。
        
        Args:
            size: 字节大小
            
        Returns:
            格式化的大小字符串
        """
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"
            
    def refresh(self) -> None:
        """刷新模型数据。"""
        self.beginResetModel()
        self._update_items()
        self.endResetModel()
        
    def add_node(self, node: Node) -> None:
        """
        添加新节点到模型。
        
        Args:
            node: 新节点
        """
        self.beginResetModel()
        self._update_items()
        self.endResetModel()
        
    def remove_node(self, node: Node) -> None:
        """
        从模型中移除节点。
        
        Args:
            node: 要移除的节点
        """
        if node in self._items:
            row = self._items.index(node)
            self.beginRemoveRows(QModelIndex(), row, row)
            self._items.remove(node)
            self.endRemoveRows()
            
    def update_node(self, node: Node) -> None:
        """
        更新节点数据。
        
        Args:
            node: 要更新的节点
        """
        if node in self._items:
            row = self._items.index(node)
            index = self.index(row, 0)
            self.dataChanged.emit(index, index)
            
    def get_parent_node(self) -> Optional[Node]:
        """获取当前父节点。"""
        return self._parent_node
        
    def get_items(self) -> List[Node]:
        """获取所有项目。"""
        return self._items.copy()
        
    def get_directories(self) -> List[Node]:
        """获取所有目录项目。"""
        return [item for item in self._items if item.is_directory()]
        
    def get_files(self) -> List[Node]:
        """获取所有文件项目。"""
        return [item for item in self._items if item.is_file()]
        
    def get_item_count(self) -> tuple[int, int]:
        """
        获取项目计数。
        
        Returns:
            (目录数量, 文件数量) 的元组
        """
        dirs = len(self.get_directories())
        files = len(self.get_files())
        return dirs, files
