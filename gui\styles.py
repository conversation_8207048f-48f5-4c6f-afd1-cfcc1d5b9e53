"""
现代化 UI 样式模块。

此模块包含类似 One Commander 的现代化深色主题样式。
"""

def get_dark_theme_stylesheet() -> str:
    """
    获取深色主题样式表。
    
    Returns:
        CSS 样式字符串
    """
    return """
    /* 主窗口样式 */
    QMainWindow {
        background-color: #1e1e1e;
        color: #ffffff;
        font-family: 'Segoe UI', 'Microsoft YaHei', 'Arial', sans-serif;
        font-size: 10pt;
        border: 1px solid #3e3e42;
    }
    
    /* 菜单栏样式 */
    QMenuBar {
        background-color: #2d2d30;
        color: #ffffff;
        border: none;
        padding: 2px;
    }
    
    QMenuBar::item {
        background-color: transparent;
        padding: 6px 12px;
        border-radius: 4px;
        margin: 1px;
    }
    
    QMenuBar::item:selected {
        background-color: #3e3e42;
    }
    
    QMenuBar::item:pressed {
        background-color: #007acc;
    }
    
    /* 菜单样式 */
    QMenu {
        background-color: #2d2d30;
        color: #ffffff;
        border: 1px solid #3e3e42;
        border-radius: 4px;
        padding: 4px;
    }
    
    QMenu::item {
        padding: 6px 20px;
        border-radius: 4px;
        margin: 1px;
    }
    
    QMenu::item:selected {
        background-color: #007acc;
    }
    
    QMenu::separator {
        height: 1px;
        background-color: #3e3e42;
        margin: 4px 0px;
    }
    
    /* 工具栏样式 */
    QToolBar {
        background-color: #2d2d30;
        border: none;
        spacing: 2px;
        padding: 4px;
    }
    
    QToolBar::separator {
        background-color: #3e3e42;
        width: 1px;
        margin: 4px 2px;
    }
    
    /* 工具按钮样式 */
    QToolButton {
        background-color: transparent;
        color: #ffffff;
        border: 1px solid transparent;
        border-radius: 6px;
        padding: 8px 12px;
        margin: 2px;
        font-weight: 500;
    }
    
    QToolButton:hover {
        background-color: #3e3e42;
        border-color: #007acc;
    }
    
    QToolButton:pressed {
        background-color: #007acc;
    }
    
    QToolButton:checked {
        background-color: #007acc;
        border-color: #007acc;
    }
    
    /* 树视图样式 */
    QTreeView {
        background-color: #252526;
        color: #cccccc;
        border: 1px solid #3e3e42;
        border-radius: 8px;
        selection-background-color: #007acc;
        selection-color: #ffffff;
        outline: none;
        font-size: 10pt;
        padding: 4px;
        gridline-color: #3e3e42;
        show-decoration-selected: 1;
    }
    
    QTreeView::item {
        padding: 4px;
        border: none;
        border-radius: 3px;
        margin: 1px;
    }
    
    QTreeView::item:hover {
        background-color: #2a2d2e;
    }
    
    QTreeView::item:selected {
        background-color: #007acc;
        color: #ffffff;
    }
    
    QTreeView::branch {
        background-color: transparent;
    }
    
    QTreeView::branch:has-siblings:!adjoins-item {
        border-image: none;
        border: none;
    }
    
    QTreeView::branch:has-siblings:adjoins-item {
        border-image: none;
        border: none;
    }
    
    QTreeView::branch:!has-children:!has-siblings:adjoins-item {
        border-image: none;
        border: none;
    }
    
    QTreeView::branch:closed:has-children:has-siblings {
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYgNEwxMCA4TDYgMTJWNFoiIGZpbGw9IiNjY2NjY2MiLz4KPC9zdmc+);
    }
    
    QTreeView::branch:open:has-children:has-siblings {
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkwxMiA2TDggMTBMNCA2WiIgZmlsbD0iI2NjY2NjYyIvPgo8L3N2Zz4=);
    }
    
    /* 列表视图样式 */
    QListView {
        background-color: #1e1e1e;
        color: #cccccc;
        border: 1px solid #3e3e42;
        border-radius: 8px;
        selection-background-color: #007acc;
        selection-color: #ffffff;
        outline: none;
        font-size: 10pt;
        padding: 4px;
        gridline-color: #3e3e42;
        show-decoration-selected: 1;
    }
    
    QListView::item {
        padding: 8px;
        border: none;
        border-radius: 4px;
        margin: 2px;
    }
    
    QListView::item:hover {
        background-color: #2a2d2e;
    }
    
    QListView::item:selected {
        background-color: #007acc;
        color: #ffffff;
    }
    
    /* 状态栏样式 */
    QStatusBar {
        background-color: #007acc;
        color: #ffffff;
        border: none;
        font-weight: 500;
    }
    
    QStatusBar::item {
        border: none;
    }
    
    QLabel {
        color: #ffffff;
        padding: 2px 8px;
    }
    
    /* 分割器样式 */
    QSplitter::handle {
        background-color: #3e3e42;
        width: 2px;
        height: 2px;
    }
    
    QSplitter::handle:hover {
        background-color: #007acc;
    }
    
    /* 滚动条样式 */
    QScrollBar:vertical {
        background-color: #2d2d30;
        width: 12px;
        border-radius: 6px;
        margin: 0px;
    }
    
    QScrollBar::handle:vertical {
        background-color: #555555;
        border-radius: 6px;
        min-height: 20px;
        margin: 2px;
    }
    
    QScrollBar::handle:vertical:hover {
        background-color: #007acc;
    }
    
    QScrollBar::add-line:vertical,
    QScrollBar::sub-line:vertical {
        height: 0px;
    }
    
    QScrollBar:horizontal {
        background-color: #2d2d30;
        height: 12px;
        border-radius: 6px;
        margin: 0px;
    }
    
    QScrollBar::handle:horizontal {
        background-color: #555555;
        border-radius: 6px;
        min-width: 20px;
        margin: 2px;
    }
    
    QScrollBar::handle:horizontal:hover {
        background-color: #007acc;
    }
    
    QScrollBar::add-line:horizontal,
    QScrollBar::sub-line:horizontal {
        width: 0px;
    }
    
    /* 对话框样式 */
    QDialog {
        background-color: #2d2d30;
        color: #ffffff;
        border: 1px solid #3e3e42;
        border-radius: 8px;
    }
    
    QLineEdit {
        background-color: #1e1e1e;
        color: #ffffff;
        border: 2px solid #3e3e42;
        border-radius: 6px;
        padding: 8px;
        font-size: 9pt;
    }
    
    QLineEdit:focus {
        border-color: #007acc;
    }
    
    QPushButton {
        background-color: #0e639c;
        color: #ffffff;
        border: none;
        border-radius: 6px;
        padding: 8px 16px;
        font-weight: 500;
        font-size: 9pt;
    }
    
    QPushButton:hover {
        background-color: #1177bb;
    }
    
    QPushButton:pressed {
        background-color: #005a9e;
    }
    
    QPushButton:default {
        background-color: #007acc;
    }
    """


def get_light_theme_stylesheet() -> str:
    """
    获取浅色主题样式表。
    
    Returns:
        CSS 样式字符串
    """
    return """
    /* 浅色主题样式 */
    QMainWindow {
        background-color: #ffffff;
        color: #000000;
        font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        font-size: 9pt;
    }
    
    QMenuBar {
        background-color: #f3f3f3;
        color: #000000;
        border: none;
        padding: 2px;
    }
    
    QMenuBar::item:selected {
        background-color: #e1e1e1;
    }
    
    QToolBar {
        background-color: #f3f3f3;
        border: none;
        spacing: 2px;
        padding: 4px;
    }
    
    QTreeView, QListView {
        background-color: #ffffff;
        color: #000000;
        border: 1px solid #d0d0d0;
        selection-background-color: #0078d4;
        selection-color: #ffffff;
    }
    
    QStatusBar {
        background-color: #0078d4;
        color: #ffffff;
    }
    """
