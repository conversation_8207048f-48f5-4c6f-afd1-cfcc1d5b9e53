"""
现代化 UI 样式模块。

此模块包含类似 One Commander 的现代化深色主题样式。
"""

def get_plasma_theme_stylesheet() -> str:
    """
    获取 KDE Plasma 风格主题样式表。

    Returns:
        CSS 样式字符串
    """
    return """
    /* 主窗口样式 - Plasma 风格 */
    QMainWindow {
        background-color: #eff0f1;
        color: #232629;
        font-family: 'Noto Sans', 'Oxygen', 'Ubuntu', 'Cantarell', 'Microsoft YaHei', sans-serif;
        font-size: 10pt;
        border: none;
    }
    
    /* 菜单栏样式 - Plasma 风格 */
    QMenuBar {
        background-color: #fcfcfc;
        color: #232629;
        border: none;
        border-bottom: 1px solid #bdc3c7;
        padding: 4px;
        font-weight: 500;
    }

    QMenuBar::item {
        background-color: transparent;
        padding: 8px 16px;
        border-radius: 6px;
        margin: 2px;
    }

    QMenuBar::item:selected {
        background-color: #3daee9;
        color: #ffffff;
    }

    QMenuBar::item:pressed {
        background-color: #2980b9;
        color: #ffffff;
    }
    
    /* 菜单样式 - Plasma 风格 */
    QMenu {
        background-color: #fcfcfc;
        color: #232629;
        border: 1px solid #bdc3c7;
        border-radius: 8px;
        padding: 8px;
    }

    QMenu::item {
        padding: 8px 24px;
        border-radius: 6px;
        margin: 2px;
        font-weight: 500;
    }

    QMenu::item:selected {
        background-color: #3daee9;
        color: #ffffff;
    }

    QMenu::separator {
        height: 1px;
        background-color: #bdc3c7;
        margin: 8px 4px;
    }
    
    /* 工具栏样式 - Plasma 风格 */
    QToolBar {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #ffffff, stop: 1 #f5f5f5);
        border: none;
        border-bottom: 1px solid #d0d0d0;
        spacing: 4px;
        padding: 8px;
    }

    QToolBar::separator {
        background-color: #bdc3c7;
        width: 1px;
        margin: 8px 4px;
    }

    /* 工具按钮样式 - Plasma 风格 */
    QToolButton {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #ffffff, stop: 1 #f8f8f8);
        color: #232629;
        border: 1px solid #d0d0d0;
        border-radius: 6px;
        padding: 8px 12px;
        margin: 2px;
        font-weight: 500;
        min-width: 70px;
    }

    QToolButton:hover {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #3daee9, stop: 1 #2980b9);
        color: #ffffff;
        border-color: #2980b9;
    }

    QToolButton:pressed {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #2980b9, stop: 1 #1f5f8b);
        color: #ffffff;
        border-color: #1f5f8b;
    }

    QToolButton:checked {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #3daee9, stop: 1 #2980b9);
        color: #ffffff;
        border-color: #2980b9;
    }
    
    /* 树视图样式 - Plasma 风格 */
    QTreeView {
        background-color: #ffffff;
        color: #232629;
        border: 1px solid #d0d0d0;
        border-radius: 6px;
        selection-background-color: #3daee9;
        selection-color: #ffffff;
        outline: none;
        font-size: 10pt;
        padding: 4px;
        gridline-color: #ecf0f1;
        show-decoration-selected: 1;
        alternate-background-color: #f8f9fa;
    }
    
    QTreeView::item {
        padding: 8px;
        border: none;
        border-radius: 6px;
        margin: 2px;
        min-height: 24px;
    }

    QTreeView::item:hover {
        background-color: #e8f4fd;
        color: #232629;
    }

    QTreeView::item:selected {
        background-color: #3daee9;
        color: #ffffff;
    }

    QTreeView::item:selected:hover {
        background-color: #2980b9;
        color: #ffffff;
    }
    
    QTreeView::branch {
        background-color: transparent;
    }
    
    QTreeView::branch:has-siblings:!adjoins-item {
        border-image: none;
        border: none;
    }
    
    QTreeView::branch:has-siblings:adjoins-item {
        border-image: none;
        border: none;
    }
    
    QTreeView::branch:!has-children:!has-siblings:adjoins-item {
        border-image: none;
        border: none;
    }
    
    QTreeView::branch:closed:has-children:has-siblings {
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYgNEwxMCA4TDYgMTJWNFoiIGZpbGw9IiNjY2NjY2MiLz4KPC9zdmc+);
    }
    
    QTreeView::branch:open:has-children:has-siblings {
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkwxMiA2TDggMTBMNCA2WiIgZmlsbD0iI2NjY2NjYyIvPgo8L3N2Zz4=);
    }
    
    /* 列表视图样式 - Plasma 风格 */
    QListView {
        background-color: #ffffff;
        color: #232629;
        border: 1px solid #d0d0d0;
        border-radius: 6px;
        selection-background-color: #3daee9;
        selection-color: #ffffff;
        outline: none;
        font-size: 10pt;
        padding: 4px;
        gridline-color: #ecf0f1;
        show-decoration-selected: 1;
        alternate-background-color: #f8f9fa;
    }

    QListView::item {
        padding: 12px;
        border: none;
        border-radius: 6px;
        margin: 3px;
        min-height: 32px;
    }

    QListView::item:hover {
        background-color: #e8f4fd;
        color: #232629;
    }

    QListView::item:selected {
        background-color: #3daee9;
        color: #ffffff;
    }

    QListView::item:selected:hover {
        background-color: #2980b9;
        color: #ffffff;
    }
    
    /* 状态栏样式 - Plasma 风格 */
    QStatusBar {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #f5f5f5, stop: 1 #e8e8e8);
        color: #232629;
        border: none;
        border-top: 1px solid #d0d0d0;
        font-weight: 500;
        padding: 4px;
    }

    QStatusBar::item {
        border: none;
        padding: 4px 8px;
    }

    QStatusBar QLabel {
        color: #232629;
        padding: 4px 12px;
        background-color: transparent;
        border-radius: 4px;
    }
    
    /* 分割器样式 */
    QSplitter::handle {
        background-color: #3e3e42;
        width: 2px;
        height: 2px;
    }
    
    QSplitter::handle:hover {
        background-color: #007acc;
    }
    
    /* 滚动条样式 - Plasma 风格 */
    QScrollBar:vertical {
        background-color: #f5f5f5;
        width: 12px;
        border-radius: 6px;
        margin: 0px;
        border: 1px solid #e0e0e0;
    }

    QScrollBar::handle:vertical {
        background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                   stop: 0 #3daee9, stop: 1 #2980b9);
        border-radius: 6px;
        min-height: 30px;
        margin: 2px;
        border: 1px solid #2980b9;
    }

    QScrollBar::handle:vertical:hover {
        background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                   stop: 0 #5dade2, stop: 1 #3498db);
    }

    QScrollBar::add-line:vertical,
    QScrollBar::sub-line:vertical {
        height: 0px;
    }

    QScrollBar:horizontal {
        background-color: #f5f5f5;
        height: 12px;
        border-radius: 6px;
        margin: 0px;
        border: 1px solid #e0e0e0;
    }

    QScrollBar::handle:horizontal {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #3daee9, stop: 1 #2980b9);
        border-radius: 6px;
        min-width: 30px;
        margin: 2px;
        border: 1px solid #2980b9;
    }

    QScrollBar::handle:horizontal:hover {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #5dade2, stop: 1 #3498db);
    }

    QScrollBar::add-line:horizontal,
    QScrollBar::sub-line:horizontal {
        width: 0px;
    }
    
    /* 对话框样式 - Plasma 风格 */
    QDialog {
        background-color: #fcfcfc;
        color: #232629;
        border: 1px solid #bdc3c7;
        border-radius: 12px;
    }

    QLineEdit {
        background-color: #ffffff;
        color: #232629;
        border: 2px solid #bdc3c7;
        border-radius: 8px;
        padding: 12px;
        font-size: 10pt;
        selection-background-color: #3daee9;
        selection-color: #ffffff;
    }

    QLineEdit:focus {
        border-color: #3daee9;
        background-color: #ffffff;
    }

    QPushButton {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #3daee9, stop: 1 #2980b9);
        color: #ffffff;
        border: 1px solid #2980b9;
        border-radius: 8px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 10pt;
        min-width: 80px;
    }

    QPushButton:hover {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #5dade2, stop: 1 #3498db);
        border-color: #3498db;
    }

    QPushButton:pressed {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #2980b9, stop: 1 #1f5f8b);
        border-color: #1f5f8b;
    }

    QPushButton:default {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #3daee9, stop: 1 #2980b9);
        border: 2px solid #2980b9;
    }
    """


def get_light_theme_stylesheet() -> str:
    """
    获取浅色主题样式表。
    
    Returns:
        CSS 样式字符串
    """
    return """
    /* 浅色主题样式 */
    QMainWindow {
        background-color: #ffffff;
        color: #000000;
        font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        font-size: 9pt;
    }
    
    QMenuBar {
        background-color: #f3f3f3;
        color: #000000;
        border: none;
        padding: 2px;
    }
    
    QMenuBar::item:selected {
        background-color: #e1e1e1;
    }
    
    QToolBar {
        background-color: #f3f3f3;
        border: none;
        spacing: 2px;
        padding: 4px;
    }
    
    QTreeView, QListView {
        background-color: #ffffff;
        color: #000000;
        border: 1px solid #d0d0d0;
        selection-background-color: #0078d4;
        selection-color: #ffffff;
    }
    
    QStatusBar {
        background-color: #0078d4;
        color: #ffffff;
    }
    """
