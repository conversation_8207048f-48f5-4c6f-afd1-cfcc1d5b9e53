"""
现代化图标管理器。

此模块提供 SVG 图标和图标管理功能，创建类似 One Commander 的现代图标。
"""

from PyQt6.QtGui import QIcon, QPixmap, QPainter
from PyQt6.QtSvg import QSvgRenderer
from PyQt6.QtCore import QByteArray, QSize
from PyQt6.QtWidgets import QStyle


class IconManager:
    """现代化图标管理器。"""
    
    def __init__(self):
        """初始化图标管理器。"""
        self._icon_cache = {}
        
    def get_icon(self, icon_name: str, size: int = 16, color: str = "#ffffff") -> QIcon:
        """
        获取绘制的图标。

        Args:
            icon_name: 图标名称
            size: 图标大小
            color: 图标颜色

        Returns:
            QIcon 对象
        """
        cache_key = f"{icon_name}_{size}_{color}"

        if cache_key not in self._icon_cache:
            svg_data = self._get_svg_data(icon_name, color)
            if svg_data:
                icon = self._create_icon_from_svg(svg_data, size)
                self._icon_cache[cache_key] = icon
            else:
                # 如果没有找到 SVG，返回空图标
                self._icon_cache[cache_key] = QIcon()

        return self._icon_cache[cache_key]
        
    def _get_svg_data(self, icon_name: str, color: str) -> str:
        """获取 SVG 数据。"""
        svg_templates = {
            "folder_open": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="folderGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#3daee9;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M18 4H9.5L8 2.5H2C1.45 2.5 1 2.95 1 3.5V16.5C1 17.05 1.45 17.5 2 17.5H18C18.55 17.5 19 17.05 19 16.5V5.5C19 4.95 18.55 4.5 18 4.5V4Z" fill="url(#folderGrad)" stroke="#2980b9" stroke-width="0.5"/>
                    <path d="M2 6H18V15.5H2V6Z" fill="#ffffff" fill-opacity="0.2"/>
                </svg>
            """,
            "folder": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="folderGrad2" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#3daee9;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M18 4H9.5L8 2.5H2C1.45 2.5 1 2.95 1 3.5V16.5C1 17.05 1.45 17.5 2 17.5H18C18.55 17.5 19 17.05 19 16.5V5.5C19 4.95 18.55 4.5 18 4.5V4Z" fill="url(#folderGrad2)" stroke="#2980b9" stroke-width="0.5"/>
                </svg>
            """,
            "file": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="fileGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ecf0f1;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M12 1H4C3.45 1 3 1.45 3 2V18C3 18.55 3.45 19 4 19H16C16.55 19 17 18.55 17 18V6L12 1Z" fill="url(#fileGrad)" stroke="#bdc3c7" stroke-width="0.5"/>
                    <path d="M12 1V6H17" fill="none" stroke="#bdc3c7" stroke-width="0.5"/>
                    <circle cx="10" cy="10" r="2" fill="{color}"/>
                </svg>
            """,
            "add_folder": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="addFolderGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#229954;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M18 4H9.5L8 2.5H2C1.45 2.5 1 2.95 1 3.5V16.5C1 17.05 1.45 17.5 2 17.5H18C18.55 17.5 19 17.05 19 16.5V5.5C19 4.95 18.55 4.5 18 4.5V4Z" fill="url(#addFolderGrad)" stroke="#229954" stroke-width="0.5"/>
                    <circle cx="14" cy="6" r="3" fill="#27ae60" stroke="#ffffff" stroke-width="1"/>
                    <path d="M13 6H15M14 5V7" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round"/>
                </svg>
            """,
            "add_file": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="addFileGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ecf0f1;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M12 1H4C3.45 1 3 1.45 3 2V18C3 18.55 3.45 19 4 19H16C16.55 19 17 18.55 17 18V6L12 1Z" fill="url(#addFileGrad)" stroke="#bdc3c7" stroke-width="0.5"/>
                    <path d="M12 1V6H17" fill="none" stroke="#bdc3c7" stroke-width="0.5"/>
                    <circle cx="14" cy="4" r="2.5" fill="#27ae60" stroke="#ffffff" stroke-width="1"/>
                    <path d="M13.2 4H14.8M14 3.2V4.8" stroke="#ffffff" stroke-width="1.2" stroke-linecap="round"/>
                </svg>
            """,
            "edit": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="editGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f39c12;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#e67e22;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M14 2L16 4L6 14L2 15L3 11L13 1C13.5 0.5 14.5 0.5 15 1L14 2Z" fill="url(#editGrad)" stroke="#d35400" stroke-width="0.5"/>
                    <path d="M13 3L15 5" stroke="#ffffff" stroke-width="1" stroke-linecap="round"/>
                    <circle cx="3" cy="15" r="1" fill="#e67e22"/>
                </svg>
            """,
            "delete": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="deleteGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M8 2V1H12V2H16V4H15V17C15 17.55 14.55 18 14 18H6C5.45 18 5 17.55 5 17V4H4V2H8Z" fill="url(#deleteGrad)" stroke="#a93226" stroke-width="0.5"/>
                    <rect x="7" y="6" width="1.5" height="8" fill="#ffffff" rx="0.75"/>
                    <rect x="10.5" y="6" width="1.5" height="8" fill="#ffffff" rx="0.75"/>
                    <rect x="5" y="3" width="10" height="1" fill="#ffffff" rx="0.5"/>
                </svg>
            """,
            "search": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="searchGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#9b59b6;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#8e44ad;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <circle cx="8" cy="8" r="6" fill="none" stroke="url(#searchGrad)" stroke-width="2"/>
                    <circle cx="8" cy="8" r="4" fill="none" stroke="#ffffff" stroke-width="1"/>
                    <path d="M13 13L17 17" stroke="url(#searchGrad)" stroke-width="2.5" stroke-linecap="round"/>
                    <circle cx="17" cy="17" r="1.5" fill="#8e44ad"/>
                </svg>
            """,
            "refresh": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="refreshGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#1abc9c;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#16a085;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M10 3C6.13 3 3 6.13 3 10C3 13.87 6.13 17 10 17C13.87 17 17 13.87 17 10" fill="none" stroke="url(#refreshGrad)" stroke-width="2" stroke-linecap="round"/>
                    <path d="M14 7L17 10L14 13" fill="none" stroke="url(#refreshGrad)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="10" cy="10" r="2" fill="#16a085"/>
                </svg>
            """,
            "arrow_right": f"""
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6 4L10 8L6 12V4Z" fill="{color}"/>
                </svg>
            """,
            "arrow_down": f"""
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 6L12 6L8 10L4 6Z" fill="{color}"/>
                </svg>
            """
        }
        
        return svg_templates.get(icon_name, "")
        
    def _create_icon_from_svg(self, svg_data: str, size: int) -> QIcon:
        """从 SVG 数据创建图标。"""
        try:
            # 创建 SVG 渲染器
            svg_bytes = QByteArray(svg_data.encode('utf-8'))
            renderer = QSvgRenderer(svg_bytes)

            # 检查是否有有效的 QApplication
            from PyQt6.QtWidgets import QApplication
            if QApplication.instance() is None:
                return QIcon()  # 如果没有 QApplication，返回空图标

            # 创建像素图
            pixmap = QPixmap(QSize(size, size))
            pixmap.fill(0)  # 透明背景

            # 渲染 SVG 到像素图
            painter = QPainter(pixmap)
            renderer.render(painter)
            painter.end()

            return QIcon(pixmap)
        except Exception:
            return QIcon()
            
    def get_file_icon(self, filename: str, size: int = 16) -> QIcon:
        """
        根据文件扩展名获取文件图标。
        
        Args:
            filename: 文件名
            size: 图标大小
            
        Returns:
            QIcon 对象
        """
        ext = filename.lower().split('.')[-1] if '.' in filename else ''
        
        # 根据扩展名选择颜色
        color_map = {
            'py': '#3776ab',      # Python 蓝色
            'js': '#f7df1e',      # JavaScript 黄色
            'html': '#e34f26',    # HTML 橙色
            'css': '#1572b6',     # CSS 蓝色
            'json': '#000000',    # JSON 黑色
            'xml': '#0060ac',     # XML 蓝色
            'txt': '#ffffff',     # 文本白色
            'md': '#000000',      # Markdown 黑色
            'jpg': '#ff6b6b',     # 图片红色
            'jpeg': '#ff6b6b',
            'png': '#ff6b6b',
            'gif': '#ff6b6b',
            'svg': '#ff6b6b',
            'mp3': '#4ecdc4',     # 音频青色
            'wav': '#4ecdc4',
            'mp4': '#45b7d1',     # 视频蓝色
            'avi': '#45b7d1',
            'zip': '#95a5a6',     # 压缩文件灰色
            'rar': '#95a5a6',
            '7z': '#95a5a6',
        }
        
        color = color_map.get(ext, '#ffffff')
        return self.get_icon('file', size, color)


# 全局图标管理器实例
icon_manager = IconManager()
