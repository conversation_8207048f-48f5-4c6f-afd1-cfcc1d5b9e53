"""
现代化图标管理器。

此模块提供 SVG 图标和图标管理功能，创建类似 One Commander 的现代图标。
"""

from PyQt6.QtGui import QIcon, QPixmap, QPainter
from PyQt6.QtSvg import QSvgRenderer
from PyQt6.QtCore import QByteArray, QSize
from PyQt6.QtWidgets import QStyle


class IconManager:
    """现代化图标管理器。"""
    
    def __init__(self):
        """初始化图标管理器。"""
        self._icon_cache = {}
        
    def get_icon(self, icon_name: str, size: int = 16, color: str = "#ffffff") -> QIcon:
        """
        获取绘制的图标。

        Args:
            icon_name: 图标名称
            size: 图标大小
            color: 图标颜色

        Returns:
            QIcon 对象
        """
        cache_key = f"{icon_name}_{size}_{color}"

        if cache_key not in self._icon_cache:
            svg_data = self._get_svg_data(icon_name, color)
            if svg_data:
                icon = self._create_icon_from_svg(svg_data, size)
                self._icon_cache[cache_key] = icon
            else:
                # 如果没有找到 SVG，返回空图标
                self._icon_cache[cache_key] = QIcon()

        return self._icon_cache[cache_key]
        
    def _get_svg_data(self, icon_name: str, color: str) -> str:
        """获取 SVG 数据。"""
        svg_templates = {
            "folder_open": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="folderGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#3daee9;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M18 4H9.5L8 2.5H2C1.45 2.5 1 2.95 1 3.5V16.5C1 17.05 1.45 17.5 2 17.5H18C18.55 17.5 19 17.05 19 16.5V5.5C19 4.95 18.55 4.5 18 4.5V4Z" fill="url(#folderGrad)" stroke="#2980b9" stroke-width="0.5"/>
                    <path d="M2 6H18V15.5H2V6Z" fill="#ffffff" fill-opacity="0.2"/>
                </svg>
            """,
            "folder": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="folderGrad2" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#3daee9;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M18 4H9.5L8 2.5H2C1.45 2.5 1 2.95 1 3.5V16.5C1 17.05 1.45 17.5 2 17.5H18C18.55 17.5 19 17.05 19 16.5V5.5C19 4.95 18.55 4.5 18 4.5V4Z" fill="url(#folderGrad2)" stroke="#2980b9" stroke-width="0.5"/>
                </svg>
            """,
            "file": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="fileGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ecf0f1;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M12 1H4C3.45 1 3 1.45 3 2V18C3 18.55 3.45 19 4 19H16C16.55 19 17 18.55 17 18V6L12 1Z" fill="url(#fileGrad)" stroke="#bdc3c7" stroke-width="0.5"/>
                    <path d="M12 1V6H17" fill="none" stroke="#bdc3c7" stroke-width="0.5"/>
                    <circle cx="10" cy="10" r="2" fill="{color}"/>
                </svg>
            """,
            "add_folder": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="addFolderGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#229954;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M18 4H9.5L8 2.5H2C1.45 2.5 1 2.95 1 3.5V16.5C1 17.05 1.45 17.5 2 17.5H18C18.55 17.5 19 17.05 19 16.5V5.5C19 4.95 18.55 4.5 18 4.5V4Z" fill="url(#addFolderGrad)" stroke="#229954" stroke-width="0.5"/>
                    <circle cx="14" cy="6" r="3" fill="#27ae60" stroke="#ffffff" stroke-width="1"/>
                    <path d="M13 6H15M14 5V7" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round"/>
                </svg>
            """,
            "add_file": f"""
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="addFileGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ecf0f1;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M12 1H4C3.45 1 3 1.45 3 2V18C3 18.55 3.45 19 4 19H16C16.55 19 17 18.55 17 18V6L12 1Z" fill="url(#addFileGrad)" stroke="#bdc3c7" stroke-width="0.5"/>
                    <path d="M12 1V6H17" fill="none" stroke="#bdc3c7" stroke-width="0.5"/>
                    <circle cx="14" cy="4" r="2.5" fill="#27ae60" stroke="#ffffff" stroke-width="1"/>
                    <path d="M13.2 4H14.8M14 3.2V4.8" stroke="#ffffff" stroke-width="1.2" stroke-linecap="round"/>
                </svg>
            """,
            "edit": f"""
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11.013 1.427a1.75 1.75 0 0 1 2.474 0l1.086 1.086a1.75 1.75 0 0 1 0 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 0 1-.927-.928l.929-3.25c.081-.286.235-.547.445-.758l8.61-8.61Z" fill="{color}"/>
                </svg>
            """,
            "delete": f"""
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.5 1.75V1H9.5V1.75H12.25V3H11.5V13.25C11.5 13.94 10.94 14.5 10.25 14.5H5.75C5.06 14.5 4.5 13.94 4.5 13.25V3H3.75V1.75H6.5ZM10.5 3H5.5V13H10.5V3ZM7 4.5V11.5H8V4.5H7ZM9 4.5V11.5H10V4.5H9Z" fill="{color}"/>
                </svg>
            """,
            "search": f"""
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z" fill="{color}"/>
                </svg>
            """,
            "refresh": f"""
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z" fill="{color}"/>
                    <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z" fill="{color}"/>
                </svg>
            """,
            "arrow_right": f"""
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6 4L10 8L6 12V4Z" fill="{color}"/>
                </svg>
            """,
            "arrow_down": f"""
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 6L12 6L8 10L4 6Z" fill="{color}"/>
                </svg>
            """
        }
        
        return svg_templates.get(icon_name, "")
        
    def _create_icon_from_svg(self, svg_data: str, size: int) -> QIcon:
        """从 SVG 数据创建图标。"""
        try:
            # 创建 SVG 渲染器
            svg_bytes = QByteArray(svg_data.encode('utf-8'))
            renderer = QSvgRenderer(svg_bytes)

            # 检查是否有有效的 QApplication
            from PyQt6.QtWidgets import QApplication
            if QApplication.instance() is None:
                return QIcon()  # 如果没有 QApplication，返回空图标

            # 创建像素图
            pixmap = QPixmap(QSize(size, size))
            pixmap.fill(0)  # 透明背景

            # 渲染 SVG 到像素图
            painter = QPainter(pixmap)
            renderer.render(painter)
            painter.end()

            return QIcon(pixmap)
        except Exception:
            return QIcon()
            
    def get_file_icon(self, filename: str, size: int = 16) -> QIcon:
        """
        根据文件扩展名获取文件图标。
        
        Args:
            filename: 文件名
            size: 图标大小
            
        Returns:
            QIcon 对象
        """
        ext = filename.lower().split('.')[-1] if '.' in filename else ''
        
        # 根据扩展名选择颜色
        color_map = {
            'py': '#3776ab',      # Python 蓝色
            'js': '#f7df1e',      # JavaScript 黄色
            'html': '#e34f26',    # HTML 橙色
            'css': '#1572b6',     # CSS 蓝色
            'json': '#000000',    # JSON 黑色
            'xml': '#0060ac',     # XML 蓝色
            'txt': '#ffffff',     # 文本白色
            'md': '#000000',      # Markdown 黑色
            'jpg': '#ff6b6b',     # 图片红色
            'jpeg': '#ff6b6b',
            'png': '#ff6b6b',
            'gif': '#ff6b6b',
            'svg': '#ff6b6b',
            'mp3': '#4ecdc4',     # 音频青色
            'wav': '#4ecdc4',
            'mp4': '#45b7d1',     # 视频蓝色
            'avi': '#45b7d1',
            'zip': '#95a5a6',     # 压缩文件灰色
            'rar': '#95a5a6',
            '7z': '#95a5a6',
        }
        
        color = color_map.get(ext, '#ffffff')
        return self.get_icon('file', size, color)


# 全局图标管理器实例
icon_manager = IconManager()
