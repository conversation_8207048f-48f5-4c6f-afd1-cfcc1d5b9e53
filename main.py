#!/usr/bin/env python3
"""
目录管理器应用程序入口点。

这是一个功能完整的目录管理器应用程序，使用 PyQt6 构建，
专为 Linux 环境（特别是 WSL2）设计。

特性：
- 双面板界面（树视图 + 列表视图）
- 内存树数据结构
- 文件和目录操作（创建、删除、重命名）
- 搜索功能
- 上下文菜单
- 工具栏和状态栏

作者: Augment Agent
版本: 1.0.0
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PyQt6.QtWidgets import QApplication, QMessageBox
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QIcon
except ImportError:
    print("错误: 未找到 PyQt6。请安装 PyQt6:")
    print("pip install PyQt6")
    sys.exit(1)

from gui.main_window import MainWindow


class DirectoryManagerApp:
    """目录管理器应用程序类。"""
    
    def __init__(self):
        """初始化应用程序。"""
        self.app = None
        self.main_window = None
        
    def setup_application(self) -> None:
        """设置应用程序。"""
        # 创建 QApplication 实例
        self.app = QApplication(sys.argv)
        
        # 设置应用程序属性
        self.app.setApplicationName("目录管理器")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("Augment Code")
        self.app.setOrganizationDomain("augmentcode.com")
        
        # 设置应用程序样式
        self.app.setStyle("Fusion")  # 使用 Fusion 样式，在 Linux 上效果较好
        
        # 设置应用程序图标（如果有的话）
        # self.app.setWindowIcon(QIcon("icons/app_icon.png"))
        
    def create_main_window(self) -> None:
        """创建主窗口。"""
        self.main_window = MainWindow()
        
        # 设置窗口属性
        self.main_window.setWindowTitle("目录管理器 v1.0.0")
        
        # 显示窗口
        self.main_window.show()
        
        # 如果有命令行参数指定目录，则打开该目录
        if len(sys.argv) > 1:
            directory_path = sys.argv[1]
            if os.path.isdir(directory_path):
                try:
                    root_node = self.main_window.fs_manager.load_directory_tree(directory_path)
                    self.main_window.tree_model.set_root_node(root_node)
                    self.main_window.current_node = root_node
                    self.main_window.list_model.set_parent_node(root_node)
                    self.main_window.update_status_bar()
                    
                    # 展开根节点
                    root_index = self.main_window.tree_model.index(0, 0)
                    if root_index.isValid():
                        self.main_window.tree_view.expand(root_index)
                        self.main_window.tree_view.setCurrentIndex(root_index)
                        
                except OSError as e:
                    QMessageBox.critical(
                        self.main_window, 
                        "错误", 
                        f"无法打开目录 '{directory_path}': {e}"
                    )
            else:
                QMessageBox.warning(
                    self.main_window,
                    "警告",
                    f"指定的路径不是有效目录: {directory_path}"
                )
                
    def run(self) -> int:
        """运行应用程序。"""
        try:
            self.setup_application()
            self.create_main_window()
            
            # 进入事件循环
            return self.app.exec()
            
        except Exception as e:
            print(f"应用程序启动失败: {e}")
            return 1


def check_environment() -> bool:
    """检查运行环境。"""
    # 检查 Python 版本
    if sys.version_info < (3, 9):
        print("错误: 需要 Python 3.9 或更高版本")
        return False
        
    # 检查是否在 Linux 环境中
    if sys.platform not in ['linux', 'linux2']:
        print("警告: 此应用程序专为 Linux 环境设计")
        print("在其他操作系统上可能无法正常工作")
        
    # 检查显示环境（对于 WSL2）
    if 'DISPLAY' not in os.environ and 'WAYLAND_DISPLAY' not in os.environ:
        print("警告: 未检测到图形显示环境")
        print("如果您在 WSL2 中运行，请确保已配置 X11 转发或安装了 WSLg")
        
    return True


def print_usage() -> None:
    """打印使用说明。"""
    print("目录管理器 v1.0.0")
    print()
    print("用法:")
    print("  python main.py [目录路径]")
    print()
    print("参数:")
    print("  目录路径    可选，指定要打开的初始目录")
    print()
    print("示例:")
    print("  python main.py")
    print("  python main.py /home/<USER>/documents")
    print("  python main.py .")
    print()
    print("快捷键:")
    print("  Ctrl+O      打开目录")
    print("  Ctrl+N      新建文件")
    print("  Ctrl+Shift+N 新建文件夹")
    print("  F2          重命名")
    print("  Delete      删除")
    print("  Ctrl+F      搜索")
    print("  F5          刷新")
    print("  Ctrl+Q      退出")


def main() -> int:
    """主函数。"""
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print_usage()
        return 0
        
    # 检查运行环境
    if not check_environment():
        return 1
        
    # 创建并运行应用程序
    app = DirectoryManagerApp()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
