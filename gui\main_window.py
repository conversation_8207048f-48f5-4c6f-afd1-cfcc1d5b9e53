"""
主窗口模块。

此模块包含应用程序的主窗口类，实现双面板布局和用户界面。
"""

import os
import sys
from typing import Optional

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, QSplitter,
    QTreeView, QListView, QToolBar, QStatusBar, QMenuBar, QMenu,
    QMessageBox, QInputDialog, QFileDialog, QLabel, QLineEdit,
    QPushButton, QDialog, QDialogButtonBox
)
from PyQt6.QtCore import Qt, QModelIndex, pyqtSignal
from PyQt6.QtGui import QAction, QKeySequence

from models.node import Node, NodeType
from core.filesystem_manager import FileSystemManager
from gui.tree_model import TreeModel
from gui.list_model import ListModel
from gui.context_menu import ContextMenuManager


class SearchDialog(QDialog):
    """搜索对话框。"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("搜索文件和目录")
        self.setModal(True)
        self.resize(400, 150)
        
        layout = QVBoxLayout()
        
        # 搜索输入
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入搜索关键词...")
        layout.addWidget(QLabel("搜索:"))
        layout.addWidget(self.search_input)
        
        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)
        self.search_input.setFocus()
        
    def get_search_text(self) -> str:
        """获取搜索文本。"""
        return self.search_input.text().strip()


class MainWindow(QMainWindow):
    """
    主窗口类。
    
    实现双面板文件管理器界面，包括树视图、列表视图、工具栏和状态栏。
    """
    
    def __init__(self):
        """初始化主窗口。"""
        super().__init__()
        
        # 初始化组件
        self.fs_manager = FileSystemManager()
        self.tree_model = TreeModel()
        self.list_model = ListModel()
        self.context_menu_manager = ContextMenuManager(self)

        # 当前选中的节点
        self.current_node: Optional[Node] = None
        
        # 设置窗口
        self.setWindowTitle("目录管理器")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1000, 700)
        
        # 创建界面
        self._create_ui()
        self._create_menus()
        self._create_toolbar()
        self._create_status_bar()
        self._connect_signals()
        
    def _create_ui(self) -> None:
        """创建用户界面。"""
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧树视图
        self.tree_view = QTreeView()
        self.tree_view.setModel(self.tree_model)
        self.tree_view.setHeaderHidden(True)
        self.tree_view.setMinimumWidth(300)
        self.tree_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.tree_view.customContextMenuRequested.connect(self.show_tree_context_menu)
        splitter.addWidget(self.tree_view)

        # 右侧列表视图
        self.list_view = QListView()
        self.list_view.setModel(self.list_model)
        self.list_view.setMinimumWidth(400)
        self.list_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.list_view.customContextMenuRequested.connect(self.show_list_context_menu)
        splitter.addWidget(self.list_view)
        
        # 设置分割器比例
        splitter.setSizes([400, 800])
        splitter.setStretchFactor(0, 0)  # 左侧面板不拉伸
        splitter.setStretchFactor(1, 1)  # 右侧面板可拉伸
        
    def _create_menus(self) -> None:
        """创建菜单栏。"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 打开目录
        open_action = QAction("打开目录(&O)", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.triggered.connect(self.open_directory)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")
        
        # 新建文件夹
        new_folder_action = QAction("新建文件夹(&F)", self)
        new_folder_action.setShortcut(QKeySequence("Ctrl+Shift+N"))
        new_folder_action.triggered.connect(self.create_folder)
        edit_menu.addAction(new_folder_action)
        
        # 新建文件
        new_file_action = QAction("新建文件(&N)", self)
        new_file_action.setShortcut(QKeySequence("Ctrl+N"))
        new_file_action.triggered.connect(self.create_file)
        edit_menu.addAction(new_file_action)
        
        edit_menu.addSeparator()
        
        # 重命名
        rename_action = QAction("重命名(&R)", self)
        rename_action.setShortcut(QKeySequence("F2"))
        rename_action.triggered.connect(self.rename_item)
        edit_menu.addAction(rename_action)
        
        # 删除
        delete_action = QAction("删除(&D)", self)
        delete_action.setShortcut(QKeySequence.StandardKey.Delete)
        delete_action.triggered.connect(self.delete_item)
        edit_menu.addAction(delete_action)
        
        edit_menu.addSeparator()
        
        # 搜索
        search_action = QAction("搜索(&S)", self)
        search_action.setShortcut(QKeySequence.StandardKey.Find)
        search_action.triggered.connect(self.search_items)
        edit_menu.addAction(search_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 刷新
        refresh_action = QAction("刷新(&R)", self)
        refresh_action.setShortcut(QKeySequence.StandardKey.Refresh)
        refresh_action.triggered.connect(self.refresh_view)
        view_menu.addAction(refresh_action)
        
    def _create_toolbar(self) -> None:
        """创建工具栏。"""
        toolbar = QToolBar("主工具栏")
        self.addToolBar(toolbar)
        
        # 打开目录
        open_action = QAction("📁 打开目录", self)
        open_action.triggered.connect(self.open_directory)
        toolbar.addAction(open_action)
        
        toolbar.addSeparator()
        
        # 新建文件夹
        new_folder_action = QAction("📁+ 新建文件夹", self)
        new_folder_action.triggered.connect(self.create_folder)
        toolbar.addAction(new_folder_action)
        
        # 新建文件
        new_file_action = QAction("📄+ 新建文件", self)
        new_file_action.triggered.connect(self.create_file)
        toolbar.addAction(new_file_action)
        
        toolbar.addSeparator()
        
        # 重命名
        rename_action = QAction("✏️ 重命名", self)
        rename_action.triggered.connect(self.rename_item)
        toolbar.addAction(rename_action)
        
        # 删除
        delete_action = QAction("🗑️ 删除", self)
        delete_action.triggered.connect(self.delete_item)
        toolbar.addAction(delete_action)
        
        toolbar.addSeparator()
        
        # 搜索
        search_action = QAction("🔍 搜索", self)
        search_action.triggered.connect(self.search_items)
        toolbar.addAction(search_action)
        
        # 刷新
        refresh_action = QAction("🔄 刷新", self)
        refresh_action.triggered.connect(self.refresh_view)
        toolbar.addAction(refresh_action)
        
    def _create_status_bar(self) -> None:
        """创建状态栏。"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 路径标签
        self.path_label = QLabel("未选择目录")
        self.status_bar.addWidget(self.path_label)
        
        # 项目计数标签
        self.count_label = QLabel("")
        self.status_bar.addPermanentWidget(self.count_label)
        
    def _connect_signals(self) -> None:
        """连接信号和槽。"""
        # 树视图选择变化
        self.tree_view.selectionModel().currentChanged.connect(
            self.on_tree_selection_changed
        )
        
        # 列表视图双击
        self.list_view.doubleClicked.connect(self.on_list_double_clicked)

        # 上下文菜单信号连接
        self.context_menu_manager.create_folder_requested.connect(self.create_folder)
        self.context_menu_manager.create_file_requested.connect(self.create_file)
        self.context_menu_manager.rename_requested.connect(self.rename_specific_item)
        self.context_menu_manager.delete_requested.connect(self.delete_specific_item)
        self.context_menu_manager.refresh_requested.connect(self.refresh_view)

    def on_tree_selection_changed(self, current: QModelIndex, _: QModelIndex) -> None:
        """
        处理树视图选择变化。
        
        Args:
            current: 当前选中的索引
            previous: 之前选中的索引
        """
        node = self.tree_model.get_node(current)
        if node and node.is_directory():
            self.current_node = node
            self.list_model.set_parent_node(node)
            self.update_status_bar()
            
    def on_list_double_clicked(self, index: QModelIndex) -> None:
        """
        处理列表视图双击。
        
        Args:
            index: 双击的索引
        """
        node = self.list_model.get_node(index)
        if node and node.is_directory():
            # 在树视图中选择该目录
            tree_index = self.tree_model.get_index_for_node(node)
            if tree_index.isValid():
                self.tree_view.setCurrentIndex(tree_index)
                self.tree_view.expand(tree_index)
                
    def update_status_bar(self) -> None:
        """更新状态栏信息。"""
        if self.current_node:
            self.path_label.setText(f"路径: {self.current_node.path}")

            dirs, files = self.list_model.get_item_count()
            self.count_label.setText(f"目录: {dirs}, 文件: {files}")
        else:
            self.path_label.setText("未选择目录")
            self.count_label.setText("")

    def open_directory(self) -> None:
        """打开目录。"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择目录", os.path.expanduser("~")
        )

        if directory:
            try:
                root_node = self.fs_manager.load_directory_tree(directory)
                self.tree_model.set_root_node(root_node)
                self.current_node = root_node
                self.list_model.set_parent_node(root_node)
                self.update_status_bar()

                # 展开根节点
                root_index = self.tree_model.index(0, 0)
                if root_index.isValid():
                    self.tree_view.expand(root_index)
                    self.tree_view.setCurrentIndex(root_index)

            except OSError as e:
                QMessageBox.critical(self, "错误", f"无法打开目录: {e}")

    def create_folder(self) -> None:
        """创建新文件夹。"""
        if not self.current_node or not self.current_node.is_directory():
            QMessageBox.warning(self, "警告", "请先选择一个目录")
            return

        name, ok = QInputDialog.getText(
            self, "新建文件夹", "文件夹名称:", text="新建文件夹"
        )

        if ok and name.strip():
            try:
                new_node = self.fs_manager.create_directory(self.current_node, name.strip())
                self.tree_model.add_node(self.current_node, new_node)
                self.list_model.add_node(new_node)
                self.update_status_bar()

            except (ValueError, OSError) as e:
                QMessageBox.critical(self, "错误", f"创建文件夹失败: {e}")

    def create_file(self) -> None:
        """创建新文件。"""
        if not self.current_node or not self.current_node.is_directory():
            QMessageBox.warning(self, "警告", "请先选择一个目录")
            return

        name, ok = QInputDialog.getText(
            self, "新建文件", "文件名称:", text="新建文件.txt"
        )

        if ok and name.strip():
            try:
                new_node = self.fs_manager.create_file(self.current_node, name.strip())
                self.list_model.add_node(new_node)
                self.update_status_bar()

            except (ValueError, OSError) as e:
                QMessageBox.critical(self, "错误", f"创建文件失败: {e}")

    def rename_item(self) -> None:
        """重命名选中的项目。"""
        # 获取当前选中的项目
        selected_node = None

        # 检查列表视图是否有选中项
        list_indexes = self.list_view.selectionModel().selectedIndexes()
        if list_indexes:
            selected_node = self.list_model.get_node(list_indexes[0])
        else:
            # 检查树视图是否有选中项
            tree_indexes = self.tree_view.selectionModel().selectedIndexes()
            if tree_indexes:
                selected_node = self.tree_model.get_node(tree_indexes[0])

        if not selected_node:
            QMessageBox.warning(self, "警告", "请先选择要重命名的项目")
            return

        if selected_node == self.fs_manager.get_root_node():
            QMessageBox.warning(self, "警告", "无法重命名根目录")
            return

        new_name, ok = QInputDialog.getText(
            self, "重命名", "新名称:", text=selected_node.name
        )

        if ok and new_name.strip() and new_name.strip() != selected_node.name:
            try:
                self.fs_manager.rename_node(selected_node, new_name.strip())

                # 更新模型
                if selected_node.is_directory():
                    self.tree_model.update_node(selected_node)
                self.list_model.update_node(selected_node)

            except (ValueError, OSError) as e:
                QMessageBox.critical(self, "错误", f"重命名失败: {e}")

    def delete_item(self) -> None:
        """删除选中的项目。"""
        # 获取当前选中的项目
        selected_node = None

        # 检查列表视图是否有选中项
        list_indexes = self.list_view.selectionModel().selectedIndexes()
        if list_indexes:
            selected_node = self.list_model.get_node(list_indexes[0])
        else:
            # 检查树视图是否有选中项
            tree_indexes = self.tree_view.selectionModel().selectedIndexes()
            if tree_indexes:
                selected_node = self.tree_model.get_node(tree_indexes[0])

        if not selected_node:
            QMessageBox.warning(self, "警告", "请先选择要删除的项目")
            return

        if selected_node == self.fs_manager.get_root_node():
            QMessageBox.warning(self, "警告", "无法删除根目录")
            return

        # 确认删除
        item_type = "目录" if selected_node.is_directory() else "文件"
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除{item_type} '{selected_node.name}' 吗？\n\n"
            f"{'此操作将删除目录及其所有内容，' if selected_node.is_directory() else ''}"
            f"且无法撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 从模型中移除
                if selected_node.is_directory():
                    self.tree_model.remove_node(selected_node)
                self.list_model.remove_node(selected_node)

                # 从文件系统删除
                self.fs_manager.delete_node(selected_node)

                self.update_status_bar()

            except (ValueError, OSError) as e:
                QMessageBox.critical(self, "错误", f"删除失败: {e}")

    def search_items(self) -> None:
        """搜索文件和目录。"""
        if not self.fs_manager.get_root_node():
            QMessageBox.warning(self, "警告", "请先打开一个目录")
            return

        dialog = SearchDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            search_text = dialog.get_search_text()
            if search_text:
                try:
                    # 从根节点开始搜索
                    root_node = self.fs_manager.get_root_node()
                    if root_node:
                        results = root_node.find_by_name(search_text, case_sensitive=False)

                        if results:
                            # 显示搜索结果
                            result_text = f"找到 {len(results)} 个匹配项:\n\n"
                            for i, node in enumerate(results[:20]):  # 最多显示20个结果
                                item_type = "目录" if node.is_directory() else "文件"
                                result_text += f"{i+1}. [{item_type}] {node.name}\n"
                                result_text += f"   路径: {node.get_relative_path(root_node.path)}\n\n"

                            if len(results) > 20:
                                result_text += f"... 还有 {len(results) - 20} 个结果"

                            QMessageBox.information(self, "搜索结果", result_text)
                        else:
                            QMessageBox.information(self, "搜索结果", "未找到匹配的项目")
                    else:
                        QMessageBox.warning(self, "警告", "没有可搜索的内容")

                except Exception as e:
                    QMessageBox.critical(self, "错误", f"搜索失败: {e}")

    def refresh_view(self) -> None:
        """刷新视图。"""
        if not self.fs_manager.get_root_node():
            return

        try:
            # 保存当前选中的路径
            current_path = None
            if self.current_node:
                current_path = self.current_node.path

            # 重新加载目录树
            root_path = self.fs_manager.get_root_path()
            root_node = self.fs_manager.load_directory_tree(root_path)
            self.tree_model.set_root_node(root_node)

            # 尝试恢复之前的选择
            if current_path:
                # 查找对应的节点
                def find_node_by_path(node: Node, target_path: str) -> Optional[Node]:
                    if node.path == target_path:
                        return node
                    for child in node.children:
                        result = find_node_by_path(child, target_path)
                        if result:
                            return result
                    return None

                restored_node = find_node_by_path(root_node, current_path)
                if restored_node and restored_node.is_directory():
                    self.current_node = restored_node
                    self.list_model.set_parent_node(restored_node)

                    # 在树视图中选择该节点
                    tree_index = self.tree_model.get_index_for_node(restored_node)
                    if tree_index.isValid():
                        self.tree_view.setCurrentIndex(tree_index)
                else:
                    # 如果无法恢复，选择根节点
                    self.current_node = root_node
                    self.list_model.set_parent_node(root_node)
            else:
                self.current_node = root_node
                self.list_model.set_parent_node(root_node)

            self.update_status_bar()

        except OSError as e:
            QMessageBox.critical(self, "错误", f"刷新失败: {e}")

    def show_tree_context_menu(self, position) -> None:
        """
        显示树视图上下文菜单。

        Args:
            position: 右键点击的位置
        """
        index = self.tree_view.indexAt(position)
        node = self.tree_model.get_node(index) if index.isValid() else None

        menu = self.context_menu_manager.create_tree_context_menu(node)
        if not menu.isEmpty():
            menu.exec(self.tree_view.mapToGlobal(position))

    def show_list_context_menu(self, position) -> None:
        """
        显示列表视图上下文菜单。

        Args:
            position: 右键点击的位置
        """
        index = self.list_view.indexAt(position)
        node = self.list_model.get_node(index) if index.isValid() else None

        menu = self.context_menu_manager.create_list_context_menu(node, self.current_node)
        if not menu.isEmpty():
            menu.exec(self.list_view.mapToGlobal(position))

    def rename_specific_item(self, node: Node) -> None:
        """
        重命名指定的项目。

        Args:
            node: 要重命名的节点
        """
        if node == self.fs_manager.get_root_node():
            QMessageBox.warning(self, "警告", "无法重命名根目录")
            return

        new_name, ok = QInputDialog.getText(
            self, "重命名", "新名称:", text=node.name
        )

        if ok and new_name.strip() and new_name.strip() != node.name:
            try:
                self.fs_manager.rename_node(node, new_name.strip())

                # 更新模型
                if node.is_directory():
                    self.tree_model.update_node(node)
                self.list_model.update_node(node)

            except (ValueError, OSError) as e:
                QMessageBox.critical(self, "错误", f"重命名失败: {e}")

    def delete_specific_item(self, node: Node) -> None:
        """
        删除指定的项目。

        Args:
            node: 要删除的节点
        """
        if node == self.fs_manager.get_root_node():
            QMessageBox.warning(self, "警告", "无法删除根目录")
            return

        # 确认删除
        item_type = "目录" if node.is_directory() else "文件"
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除{item_type} '{node.name}' 吗？\n\n"
            f"{'此操作将删除目录及其所有内容，' if node.is_directory() else ''}"
            f"且无法撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 从模型中移除
                if node.is_directory():
                    self.tree_model.remove_node(node)
                self.list_model.remove_node(node)

                # 从文件系统删除
                self.fs_manager.delete_node(node)

                self.update_status_bar()

            except (ValueError, OSError) as e:
                QMessageBox.critical(self, "错误", f"删除失败: {e}")

    def closeEvent(self, event) -> None:
        """处理窗口关闭事件。"""
        # 可以在这里添加保存设置等操作
        event.accept()
