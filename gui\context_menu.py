"""
上下文菜单模块。

此模块包含用于树视图和列表视图的右键上下文菜单。
"""

from PyQt6.QtWidgets import QMenu
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtGui import QAction
from typing import Optional

from models.node import Node


class ContextMenuManager(QObject):
    """
    上下文菜单管理器。
    
    负责创建和管理树视图和列表视图的右键菜单。
    """
    
    # 信号定义
    create_folder_requested = pyqtSignal()
    create_file_requested = pyqtSignal()
    rename_requested = pyqtSignal(Node)
    delete_requested = pyqtSignal(Node)
    refresh_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        """初始化上下文菜单管理器。"""
        super().__init__(parent)
        
    def create_tree_context_menu(self, node: Optional[Node]) -> QMenu:
        """
        创建树视图的上下文菜单。
        
        Args:
            node: 右键点击的节点
            
        Returns:
            上下文菜单
        """
        menu = QMenu()
        
        if node and node.is_directory():
            # 新建文件夹
            new_folder_action = QAction("新建文件夹", menu)
            new_folder_action.triggered.connect(self.create_folder_requested.emit)
            menu.addAction(new_folder_action)

            # 新建文件
            new_file_action = QAction("新建文件", menu)
            new_file_action.triggered.connect(self.create_file_requested.emit)
            menu.addAction(new_file_action)

            menu.addSeparator()

            # 重命名（不能重命名根目录）
            if node.parent:
                rename_action = QAction("重命名", menu)
                rename_action.triggered.connect(lambda: self.rename_requested.emit(node))
                menu.addAction(rename_action)

                # 删除
                delete_action = QAction("删除", menu)
                delete_action.triggered.connect(lambda: self.delete_requested.emit(node))
                menu.addAction(delete_action)

                menu.addSeparator()

            # 刷新
            refresh_action = QAction("刷新", menu)
            refresh_action.triggered.connect(self.refresh_requested.emit)
            menu.addAction(refresh_action)
            
        return menu
        
    def create_list_context_menu(self, node: Optional[Node], parent_node: Optional[Node]) -> QMenu:
        """
        创建列表视图的上下文菜单。
        
        Args:
            node: 右键点击的节点（可能为None，表示空白区域）
            parent_node: 当前显示的父目录节点
            
        Returns:
            上下文菜单
        """
        menu = QMenu()
        
        if node:
            # 如果点击的是目录，可以进入
            if node.is_directory():
                # 新建文件夹
                new_folder_action = QAction("新建文件夹", menu)
                new_folder_action.triggered.connect(self.create_folder_requested.emit)
                menu.addAction(new_folder_action)

                # 新建文件
                new_file_action = QAction("新建文件", menu)
                new_file_action.triggered.connect(self.create_file_requested.emit)
                menu.addAction(new_file_action)

                menu.addSeparator()

            # 重命名
            rename_action = QAction("重命名", menu)
            rename_action.triggered.connect(lambda: self.rename_requested.emit(node))
            menu.addAction(rename_action)

            # 删除
            delete_action = QAction("删除", menu)
            delete_action.triggered.connect(lambda: self.delete_requested.emit(node))
            menu.addAction(delete_action)

        else:
            # 点击空白区域，显示新建选项
            if parent_node and parent_node.is_directory():
                # 新建文件夹
                new_folder_action = QAction("新建文件夹", menu)
                new_folder_action.triggered.connect(self.create_folder_requested.emit)
                menu.addAction(new_folder_action)

                # 新建文件
                new_file_action = QAction("新建文件", menu)
                new_file_action.triggered.connect(self.create_file_requested.emit)
                menu.addAction(new_file_action)

        if not menu.isEmpty():
            menu.addSeparator()

        # 刷新
        refresh_action = QAction("刷新", menu)
        refresh_action.triggered.connect(self.refresh_requested.emit)
        menu.addAction(refresh_action)
        
        return menu
